name: toii_social
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.0

dependencies:
  google_sign_in: ^6.3.0
  sign_in_with_apple:
  go_router:
  retrofit:
  get_it:
  flutter_bloc:
  shared_preferences:
  curl_logger_dio_interceptor:
  webview_flutter:
  flutter_svg:
  crypto:
  firebase_core: 
  web3dart:
  equatable:
  url_launcher:
  intl: 
  flutter:
    sdk: flutter
  easy_rich_text:
  country_code_picker:
  cupertino_icons: ^1.0.8
  reown_appkit: 1.5.0
  pinput:
  bip39:
  bip32:
  encrypt:
  dio: ^5.8.0+1
  local_auth:
  json_annotation: ^4.9.0
  flutter_secure_storage:

  flutter_localizations:
    sdk: flutter
  hex: ^0.2.0
  crystal_navigation_bar:
  device_info_plus:
  uuid:
  readmore:
  # mlx_flutter:
  #   path: library/mlx_flutter
  flutter_gen_ai_chat_ui:
  cached_network_image: 

  path_provider:
 
  # flutter_nearby_connections: 
  flutter_styled_toast:
  blur:
  shake:
  debounce_throttle:
  nearby_service:
  file_picker:
  image_picker: ^1.1.2
  image_cropper: 
  carousel_slider: ^5.1.1
  flutter_staggered_grid_view: ^0.7.0
  shimmer: ^3.0.0
  photo_view: ^0.15.0
  passkeys:
  pretty_qr_code:
  share_plus:
  mobile_scanner: ^7.0.1
  flutter_easyloading: ^3.0.5
  web3_signers:
  hive:
  hive_flutter:
  animated_tree_view: ^2.3.0
dev_dependencies:
  build_runner: ^2.4.6
  json_serializable: ^6.7.1
  retrofit_generator:
  flutter_gen_runner:
  hive_generator:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  intl_utils:

dependency_overrides:
   intl: ^0.20.1
   
flutter_intl:
  enabled: true
  main_locale: en
flutter:

  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/
    - assets/json/
  fonts:
    - family: Manrope
      fonts:
        - asset: assets/fonts/Manrope-Bold.ttf
          weight: 700
        - asset: assets/fonts/Manrope-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Manrope-Medium.ttf
          weight: 500
        - asset: assets/fonts/Manrope-Regular.ttf
          weight: 400
        - asset: assets/fonts/Manrope-Light.ttf
          weight: 300

flutter_gen:
  output: lib/gen/
  line_length: 80
  integrations:
    flutter_svg: true
