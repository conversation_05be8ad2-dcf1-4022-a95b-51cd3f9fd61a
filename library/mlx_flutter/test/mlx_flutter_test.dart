import 'package:flutter_test/flutter_test.dart';
import 'package:mlx_flutter/mlx_flutter.dart';
import 'package:mlx_flutter/mlx_flutter_platform_interface.dart';
import 'package:mlx_flutter/mlx_flutter_method_channel.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

class MockMlxFlutterPlatform
    with MockPlatformInterfaceMixin
    implements MlxFlutterPlatform {

  @override
  Future<String?> getPlatformVersion() => Future.value('42');
}

void main() {
  final MlxFlutterPlatform initialPlatform = MlxFlutterPlatform.instance;

  test('$MethodChannelMlxFlutter is the default instance', () {
    expect(initialPlatform, isInstanceOf<MethodChannelMlxFlutter>());
  });

  test('getPlatformVersion', () async {
    MlxFlutter mlxFlutterPlugin = MlxFlutter();
    MockMlxFlutterPlatform fakePlatform = MockMlxFlutterPlatform();
    MlxFlutterPlatform.instance = fakePlatform;

    expect(await mlxFlutterPlugin.getPlatformVersion(), '42');
  });
}
