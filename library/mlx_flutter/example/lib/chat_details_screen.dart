import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:mlx_flutter_example/main.dart';

class ChatDetailsScreen extends StatefulWidget {
  const ChatDetailsScreen({super.key});

  @override
  State<ChatDetailsScreen> createState() => _ChatDetailsScreenState();
}

class ChatMessage {
  String messageContent;
  String messageType;
  ChatMessage({required this.messageContent, required this.messageType});
}

class _ChatDetailsScreenState extends State<ChatDetailsScreen> {
  final _textController = TextEditingController();
  final List<ChatMessage> _messages = [];
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Chat Details")),
      body: _body(),
    );
  }

  Widget _body() {
    return SafeArea(
      child: Column(
        children: [
          Expanded(
            child: ListView.builder(
              itemCount: _messages.length,
              shrinkWrap: true,
              padding: EdgeInsets.only(top: 10, bottom: 10),

              itemBuilder: (context, index) {
                return Container(
                  padding: EdgeInsets.only(
                    left: 14,
                    right: 14,
                    top: 10,
                    bottom: 10,
                  ),
                  child: Align(
                    alignment:
                        (_messages[index].messageType == "receiver"
                            ? Alignment.topLeft
                            : Alignment.topRight),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        color:
                            (_messages[index].messageType == "receiver"
                                ? Colors.grey.shade200
                                : Colors.blue[200]),
                      ),
                      padding: EdgeInsets.all(16),
                      child: Text(
                        _messages[index].messageContent,
                        style: TextStyle(fontSize: 15),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          Row(
            children: <Widget>[
              Expanded(
                child: TextField(
                  autofocus: true,
                  controller: _textController,
                  decoration: InputDecoration(
                    hintText: "Write message...",
                    hintStyle: TextStyle(color: Colors.black54),
                    // border: InputBorder.none,
                  ),
                ),
              ),
              SizedBox(width: 15),
              FloatingActionButton(
                onPressed: () async {
                  final message = _textController.text;
                  if (message.isNotEmpty) {
                    // Send the message
                    setState(() {
                      _messages.add(
                        ChatMessage(
                          messageContent: message,
                          messageType: 'sender',
                        ),
                      );
                    });
                    _textController.clear();
                    final result = await mlxFlutterPlugin.generate(message);

                    if (result != null) {
                      setState(() {
                        _messages.add(
                          ChatMessage(
                            messageContent: result,
                            messageType: 'receiver',
                          ),
                        );
                      });
                    }
                  }
                },
                child: Icon(Icons.send, color: Colors.white, size: 18),
                backgroundColor: Colors.blue,
                elevation: 0,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
