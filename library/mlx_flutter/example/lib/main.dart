import 'package:flutter/material.dart';
import 'dart:async';

import 'package:flutter/services.dart';
import 'package:mlx_flutter/mlx_flutter.dart';
import 'package:mlx_flutter_example/chat_details_screen.dart';

void main() {
  runApp( MyApp());
}
class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Demo',
      theme: ThemeData(primarySwatch: Colors.blue),
      home: HomeScreen(),
    );
  }
}
  final mlxFlutterPlugin = MlxFlutter();

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  String _platformVersion = 'Loading model...';
  bool isLoading = false;
  bool isSuccess = false;
  @override
  void initState() {
    super.initState();
    getData();
  }

  void getData() async {
    setState(() {
      isLoading = true;
      _platformVersion = 'Loading model...';
    });
    final result = await mlxFlutterPlugin.loadModel();
    setState(() {
      isLoading = false;
      if (result == "loaded") {
        isSuccess = true;
        _platformVersion = 'Model loaded successfully';
      } else {
        _platformVersion = 'Failed to load model.';
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(title: const Text('Loading model example')),
        body: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('$_platformVersion\n'),
              if (isLoading) const SizedBox(height: 20),
              if (isLoading) const CircularProgressIndicator(),
            ],
          ),
        ),
        floatingActionButton:
            isSuccess
                ? FloatingActionButton(
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => ChatDetailsScreen(),
                      ),
                    );
                  },
                  child: const Icon(Icons.chat),
                )
                : null,
      ),
    );
  }
}
