{"originHash": "c38294d0b8d7c3835e86ae5502e98c91ac2c3072876682282f9c76b2842140b2", "pins": [{"identity": "gzipswift", "kind": "remoteSourceControl", "location": "https://github.com/1024jp/GzipSwift", "state": {"revision": "731037f6cc2be2ec01562f6597c1d0aa3fe6fd05", "version": "6.0.1"}}, {"identity": "jinja", "kind": "remoteSourceControl", "location": "https://github.com/johnmai-dev/<PERSON>ja", "state": {"revision": "fc1233dea1142897d474bda2f1f9a6c3fe7acab6", "version": "1.2.1"}}, {"identity": "mlx-swift", "kind": "remoteSourceControl", "location": "https://github.com/ml-explore/mlx-swift", "state": {"revision": "b79c74ce773440b86a81ef925ea78dd5023a16c0", "version": "0.25.5"}}, {"identity": "mlx-swift-examples", "kind": "remoteSourceControl", "location": "https://github.com/ml-explore/mlx-swift-examples.git", "state": {"branch": "main", "revision": "42b0d21a607b97c3b99d1e20626b7d9b14827b81"}}, {"identity": "swift-argument-parser", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-argument-parser.git", "state": {"revision": "0fbc8848e389af3bb55c182bc19ca9d5dc2f255b", "version": "1.4.0"}}, {"identity": "swift-collections", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-collections.git", "state": {"revision": "c1805596154bb3a265fd91b8ac0c4433b4348fb0", "version": "1.2.0"}}, {"identity": "swift-numerics", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-numerics", "state": {"revision": "e0ec0f5f3af6f3e4d5e7a19d2af26b481acb6ba8", "version": "1.0.3"}}, {"identity": "swift-transformers", "kind": "remoteSourceControl", "location": "https://github.com/huggingface/swift-transformers", "state": {"revision": "e5bf0627bd134cf8ddc407cda403ae84207af959", "version": "0.1.22"}}], "version": 3}