{"originHash": "57e2811083a19697a6545094fb16ea66e196c8d31329ab86b542c3bb600da3f8", "pins": [{"identity": "gzipswift", "kind": "remoteSourceControl", "location": "https://github.com/1024jp/GzipSwift", "state": {"revision": "731037f6cc2be2ec01562f6597c1d0aa3fe6fd05", "version": "6.0.1"}}, {"identity": "jinja", "kind": "remoteSourceControl", "location": "https://github.com/johnmai-dev/<PERSON>ja", "state": {"revision": "31c4dd39bcdc07eaa42a384bdc88ea599022b800", "version": "1.1.2"}}, {"identity": "mlx-swift", "kind": "remoteSourceControl", "location": "https://github.com/ml-explore/mlx-swift", "state": {"revision": "b79c74ce773440b86a81ef925ea78dd5023a16c0", "version": "0.25.5"}}, {"identity": "mlx-swift-examples", "kind": "remoteSourceControl", "location": "https://github.com/ml-explore/mlx-swift-examples.git", "state": {"branch": "main", "revision": "2cceb87794bcce960dfc17e79a91946f41c02317"}}, {"identity": "swift-argument-parser", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-argument-parser.git", "state": {"revision": "0fbc8848e389af3bb55c182bc19ca9d5dc2f255b", "version": "1.4.0"}}, {"identity": "swift-collections", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-collections.git", "state": {"revision": "c1805596154bb3a265fd91b8ac0c4433b4348fb0", "version": "1.2.0"}}, {"identity": "swift-numerics", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-numerics", "state": {"revision": "e0ec0f5f3af6f3e4d5e7a19d2af26b481acb6ba8", "version": "1.0.3"}}, {"identity": "swift-transformers", "kind": "remoteSourceControl", "location": "https://github.com/huggingface/swift-transformers", "state": {"revision": "c2f302a74cca59cbde683b1425ab43c05685515a", "version": "0.1.21"}}], "version": 3}