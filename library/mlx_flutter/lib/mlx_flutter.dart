import 'package:flutter/services.dart';

import 'mlx_flutter_platform_interface.dart';

class MlxFlutter {
  static const _channel = MethodChannel('mlx_flutter');
  static const _eChannel = EventChannel('mlx_flutter/events');
   Stream<dynamic> get streamData =>
      _eChannel.receiveBroadcastStream();
  Future<String?> getPlatformVersion() {
    return MlxFlutterPlatform.instance.getPlatformVersion();
  }

  Future<String> loadModel() async {
   return await _channel.invokeMethod('loadModel');
  }

  Future<String?> generate(String prompt) async {
    final version = await _channel.invokeMethod<String>('generate', prompt);
    print(version);
    return version;
  }
}
