import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'mlx_flutter_method_channel.dart';

abstract class MlxFlutterPlatform extends PlatformInterface {
  /// Constructs a MlxFlutterPlatform.
  MlxFlutterPlatform() : super(token: _token);

  static final Object _token = Object();

  static MlxFlutterPlatform _instance = MethodChannelMlxFlutter();

  /// The default instance of [MlxFlutterPlatform] to use.
  ///
  /// Defaults to [MethodChannelMlxFlutter].
  static MlxFlutterPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [MlxFlutterPlatform] when
  /// they register themselves.
  static set instance(MlxFlutterPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<String?> getPlatformVersion() {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }
}
