import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'mlx_flutter_platform_interface.dart';

/// An implementation of [MlxFlutterPlatform] that uses method channels.
class MethodChannelMlxFlutter extends MlxFlutterPlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('mlx_flutter');

  @override
  Future<String?> getPlatformVersion() async {
    final version = await methodChannel.invokeMethod<String>('getPlatformVersion');
    return version;
  }
}
