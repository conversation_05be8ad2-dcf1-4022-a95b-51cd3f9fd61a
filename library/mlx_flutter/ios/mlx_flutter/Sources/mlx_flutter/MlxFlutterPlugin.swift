import Flutter
import UIKit
import MLX
import MLXLLM
import MLXLMCommon

public class MlxFlutterPlugin: NSObject, FlutterPlugin, FlutterStreamHandler {
    private var event: FlutterEventSink?

    public func onListen(withArguments arguments: Any?, eventSink: @escaping FlutterEventSink) -> FlutterError? {
         self.event = eventSink
         return nil
     }

    public func onCancel(withArguments arguments: Any?) -> FlutterError? {
        event = nil
        return nil
    }
    
    var container: ModelContainer?

  public static func register(with registrar: FlutterPluginRegistrar) {
    let eventChannel = FlutterEventChannel(name: "mlx_flutter/events", binaryMessenger: registrar.messenger())

    let channel = FlutterMethodChannel(name: "mlx_flutter", binaryMessenger: registrar.messenger())
    let instance = MlxFlutterPlugin()
      eventChannel.setStreamHandler(instance)

    registrar.addMethodCallDelegate(instance, channel: channel)
  }

  public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
    switch call.method {
    case "loadModel":
        print("start model");
      load(result: result)
    case "generate":
      guard let prompt = call.arguments as? String else { result(FlutterError(code:"ARG", message:"Need prompt", details:nil)); return }
      generate(prompt: prompt, result: result)
    case "getPlatformVersion":
      result("iOS " + UIDevice.current.systemVersion)
    default:
      result(FlutterMethodNotImplemented)
    }
  }

   private func load(result: @escaping FlutterResult) {
    Task {
      do {
          MLX.GPU.set(cacheLimit: 20 * 1024 * 1024)
          self.container =   try await LLMModelFactory.shared.loadContainer(configuration:  LLMRegistry.llama3_2_1B_4bit) { progress in
              DispatchQueue.main.async {
                  self.event?(Int(progress.fractionCompleted * 100))
              }
           
              print("Download progress: \(Int(progress.fractionCompleted * 100))%")
          }
          result("loaded")
          print("loaded")
      } catch {
        result(FlutterError(code:"LOAD", message:error.localizedDescription, details:nil))
          print("error")
      }
    }
  }

   private func generate(prompt: String, result: @escaping FlutterResult) {
    guard let container else { result("not_loaded"); return }
    Task {
      do {
        let chat: [Chat.Message] = [.system("You are a professional medical AI assistant. Analyze the following symptoms and provide:\n1. Possible diseases (no formal diagnosis)\n2. Level of urgency\n3. Recommendations for seeking medical care\n4. Safe self-care measures\n\nNOTE: This is for reference only, not a substitute for professional medical diagnosis."), .user(prompt)]
        let userInput = UserInput(chat: chat)
        var output = ""
        try await container.perform { context in
          let input = try await context.processor.prepare(input: userInput)
          let stream = try MLXLMCommon.generate(
              input: input,
              parameters: GenerateParameters(maxTokens:2000, temperature:0.6),
              context: context)
          for try await batch in stream {
              if let chunk = batch.chunk { output += chunk }
          }
        }
        result(output)
      } catch {
        result(FlutterError(code:"GEN", message:error.localizedDescription, details:nil))
      }
    }
  }
}
