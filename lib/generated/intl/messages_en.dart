// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "biometric_not_support": MessageLookupByLibrary.simpleMessage(
      "Biometric not support",
    ),
    "biometric_use_face_id": MessageLookupByLibrary.simpleMessage(
      "Use Face ID",
    ),
    "biometric_use_fingerprint": MessageLookupByLibrary.simpleMessage(
      "Use Fingerprint",
    ),
    "biometric_use_touch_id": MessageLookupByLibrary.simpleMessage(
      "Use Touch ID",
    ),
    "login_description": MessageLookupByLibrary.simpleMessage(
      "It\'s great to see you again!",
    ),
    "login_dont_have_account": MessageLookupByLibrary.simpleMessage(
      "Don\'t have an account?",
    ),
    "login_email_or_phone": MessageLookupByLibrary.simpleMessage(
      "Email or Phone Number",
    ),
    "login_forgot_password": MessageLookupByLibrary.simpleMessage(
      "Forgot Password?",
    ),
    "login_or_sign_in_with": MessageLookupByLibrary.simpleMessage(
      "Or Join with",
    ),
    "login_password": MessageLookupByLibrary.simpleMessage("Password"),
    "login_register": MessageLookupByLibrary.simpleMessage("Register"),
    "login_sign_in": MessageLookupByLibrary.simpleMessage("Sign In"),
    "login_title": MessageLookupByLibrary.simpleMessage("Welcome Back!"),
    "login_with_wallet": MessageLookupByLibrary.simpleMessage(
      "Login With Wallet",
    ),
    "onboarding_description1": MessageLookupByLibrary.simpleMessage(
      "No Ads\nAI-Personalized.",
    ),
    "onboarding_description2": MessageLookupByLibrary.simpleMessage(
      "Decentralized",
    ),
    "onboarding_description3": MessageLookupByLibrary.simpleMessage(
      "Connect and\nExplore your way",
    ),
    "onboarding_explore_the_future": MessageLookupByLibrary.simpleMessage(
      "EXPLORE THE FUTURE",
    ),
    "onboarding_toii_social": MessageLookupByLibrary.simpleMessage(
      "Toii Social",
    ),
    "onboarding_welcome": MessageLookupByLibrary.simpleMessage("Welcome to "),
    "register_already_have_account": MessageLookupByLibrary.simpleMessage(
      "Already have an account?",
    ),
    "register_confirm_button": MessageLookupByLibrary.simpleMessage("Confirm"),
    "register_confirm_password_label": MessageLookupByLibrary.simpleMessage(
      "Confirm Password",
    ),
    "register_confirm_pin_title": MessageLookupByLibrary.simpleMessage(
      "Confirm your PIN code",
    ),
    "register_continue": MessageLookupByLibrary.simpleMessage("Continue"),
    "register_create_password": MessageLookupByLibrary.simpleMessage(
      "Create password",
    ),
    "register_create_pin_error": MessageLookupByLibrary.simpleMessage(
      "Pin not match",
    ),
    "register_create_username_error": MessageLookupByLibrary.simpleMessage(
      "Username not empty",
    ),
    "register_create_username_subtitle": MessageLookupByLibrary.simpleMessage(
      "Pick a name to use on Toii",
    ),
    "register_create_username_title": MessageLookupByLibrary.simpleMessage(
      "Create your username",
    ),
    "register_create_wallet_title": MessageLookupByLibrary.simpleMessage(
      "Create a wallet",
    ),
    "register_creating_wallet": MessageLookupByLibrary.simpleMessage(
      "Creating your wallet...",
    ),
    "register_didnt_receive_code": MessageLookupByLibrary.simpleMessage(
      "Didn\'t receive the code?",
    ),
    "register_enter_pin_description": MessageLookupByLibrary.simpleMessage(
      "Protect your wallet. PIN code increases wallet security in the event your phone is stolen",
    ),
    "register_enter_pin_title": MessageLookupByLibrary.simpleMessage(
      "Enter a PIN Code",
    ),
    "register_enter_subtitle": MessageLookupByLibrary.simpleMessage(
      "phone or Email",
    ),
    "register_enter_title": MessageLookupByLibrary.simpleMessage(
      "Enter\nphone or Email",
    ),
    "register_label_phone_number": MessageLookupByLibrary.simpleMessage(
      "Phone Number",
    ),
    "register_option_email": MessageLookupByLibrary.simpleMessage("Email"),
    "register_option_phone": MessageLookupByLibrary.simpleMessage("Phone"),
    "register_password_label": MessageLookupByLibrary.simpleMessage("Password"),
    "register_password_mismatch_error": MessageLookupByLibrary.simpleMessage(
      "Passwords do not match",
    ),
    "register_password_requirements_title":
        MessageLookupByLibrary.simpleMessage("Your password should contain:"),
    "register_password_rule_1": MessageLookupByLibrary.simpleMessage(
      "8 or more characters.",
    ),
    "register_password_rule_2": MessageLookupByLibrary.simpleMessage(
      "At least one upper case character.",
    ),
    "register_password_rule_3": MessageLookupByLibrary.simpleMessage(
      "At least one symbol.",
    ),
    "register_privacy_policy": MessageLookupByLibrary.simpleMessage(
      "Privacy Policy",
    ),
    "register_resend_code": MessageLookupByLibrary.simpleMessage("Resend"),
    "register_sign_in": MessageLookupByLibrary.simpleMessage("Sign In"),
    "register_terms_agreement": MessageLookupByLibrary.simpleMessage(
      "I have read and agree to the Terms of Service and Privacy Policy",
    ),
    "register_terms_of_service": MessageLookupByLibrary.simpleMessage(
      "Terms of Service",
    ),
    "register_timer_placeholder": MessageLookupByLibrary.simpleMessage("00:24"),
    "register_unlock_with_face_id": MessageLookupByLibrary.simpleMessage(
      "Unlock with Face ID?",
    ),
    "register_username_field_label": MessageLookupByLibrary.simpleMessage(
      "Username",
    ),
    "register_verify_email_title": MessageLookupByLibrary.simpleMessage(
      "Verify Your Email",
    ),
    "register_verify_phone_subtitle": MessageLookupByLibrary.simpleMessage(
      "Please enter the 6 digit code sent to",
    ),
    "register_verify_phone_title": MessageLookupByLibrary.simpleMessage(
      "Verify Your Phone Number",
    ),
    "start_description": MessageLookupByLibrary.simpleMessage(
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor ",
    ),
    "start_login": MessageLookupByLibrary.simpleMessage("Sign in"),
    "start_register": MessageLookupByLibrary.simpleMessage("Register"),
    "start_welcome": MessageLookupByLibrary.simpleMessage(
      "Welcome to Toii Social",
    ),
  };
}
