extension MapExtension on Map<dynamic, dynamic>? {
  void addIf(dynamic key, dynamic value, bool Function() condition) {
    if (this != null && condition()) this![key] = value;
  }

  Map<dynamic, dynamic>? removeSensitiveData() {
    if (this == null) return null;

    final newMap = {}..addAll(this!);

    return newMap
      ..removeWhere(
        (key, value) {
          final newKey = key.toString();

          final shouldRemove = newKey.contains('pin') ||
              newKey.contains('password') ||
              newKey.contains('pwd') ||
              newKey.contains('otp') ||
              newKey.contains('code');

          if (!shouldRemove && value is Map) {
            newMap[key] = value.removeSensitiveData();
          }

          return shouldRemove;
        },
      );
  }
}
