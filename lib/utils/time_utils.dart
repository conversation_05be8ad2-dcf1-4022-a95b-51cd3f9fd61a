String timeAgoSinceDate(String dateString) {
  final date = DateTime.tryParse(dateString);
  if (date == null) return '';
  final now = DateTime.now();
  final difference = now.difference(date);

  if (difference.inDays > 8) {
    return '${date.day}/${date.month}/${date.year}';
  } else if (difference.inDays >= 1) {
    return '${difference.inDays}d';
  } else if (difference.inHours >= 1) {
    return '${difference.inHours}h';
  } else if (difference.inMinutes >= 1) {
    return '${difference.inMinutes}m';
  } else {
    return 'now';
  }
}
