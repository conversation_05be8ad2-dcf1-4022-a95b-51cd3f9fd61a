import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:toii_social/utils/shared_prefs/shared_prefs.dart';
import 'package:uuid/uuid.dart';

final String deviceIDKey = 'deviceID';
Future<String> getUniqueDeviceId() async {
  String deviceIDStr = "";
  final deviceId = SharedPref.getString(deviceIDKey);
  if (deviceId.isEmpty) {
    if (Platform.isIOS) {
      var deviceInfo = DeviceInfoPlugin();

      var iosDeviceInfo = await deviceInfo.iosInfo;
      deviceIDStr = iosDeviceInfo.identifierForVendor ?? ""; // unique ID on iOS
    } else if (Platform.isAndroid) {
      deviceIDStr = const Uuid().v4();
    }
  } else {
    deviceIDStr = deviceId;
  }
  if (Platform.isIOS) {
    var deviceInfo = DeviceInfoPlugin();

    var iosDeviceInfo = await deviceInfo.iosInfo;
    deviceIDStr = iosDeviceInfo.identifierForVendor ?? ""; // unique ID on iOS
  }
  SharedPref.setString(deviceIDKey, deviceIDStr);
  return deviceIDStr;
}
