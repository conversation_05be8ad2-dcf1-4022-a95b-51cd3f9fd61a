extension EmailValidator on String {
  bool isValidEmail() {
    return RegExp(
      r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$',
    ).hasMatch(this);
  }

  bool get isPhoneNoValid {
    final newPhone = replaceAll("+", '');
    final phone = newPhone[0] != "0" ? "0$newPhone" : newPhone;

    final regExp = RegExp(r'^\+?[0-9]{7,15}$');
    return regExp.hasMatch(phone);
  }

  bool get containsUppercase => contains(RegExp(r'[A-Z]'));

  bool get containsSpecialCharacter {
    final specialCharRegex = RegExp(
      r'[\^$*.\[\]{}()?\-"!@#%&/\,><:;_~`+='
      "'"
      ']',
    );
    return specialCharRegex.hasMatch(this);
  }

  String get formattedPhoneNumber {
    final formattedPhoneNumber = trim();
    return formattedPhoneNumber[0] == "0"
        ? formattedPhoneNumber.substring(1)
        : formattedPhoneNumber;
  }
}
