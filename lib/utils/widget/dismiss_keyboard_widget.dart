import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class DismissKeyboardWidget extends StatelessWidget {
  final Widget child;
  final GestureTapCallback? onTap;
  const DismissKeyboardWidget({super.key, required this.child, this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        SystemChannels.textInput.invokeMethod('TextInput.hide');
        onTap?.call();
      },
      child: child,
    );
  }
}
