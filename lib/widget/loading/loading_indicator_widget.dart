import 'package:flutter/material.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/widget/colors/colors.dart';

class LoadingIndicatorWidget extends StatelessWidget {
  const LoadingIndicatorWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 32,
      height: 32,
      child: CircularProgressIndicator(
        color: themeData.primaryGreen500,
        strokeWidth: 2.0,
        backgroundColor: themeData.primaryGreen100,
        //  backgroundColor: Colors.transparent,
      ),
    );
  }
}
