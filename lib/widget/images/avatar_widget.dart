import 'package:flutter/material.dart';

class AvatarWidget extends StatelessWidget {
  const AvatarWidget({
    super.key,
    required this.name,
    this.imageUrl,
    this.size = 64,
    this.backgroundColor = Colors.white,
    this.nameStyle,
  });

  final String name;
  final String? imageUrl;
  final double size;
  final Color? backgroundColor;
  final TextStyle? nameStyle;

  @override
  Widget build(BuildContext context) {
    Widget avatarWidget;

    if (imageUrl != null && imageUrl!.isNotEmpty) {
      avatarWidget = CircleAvatar(
        radius: size / 2,
        backgroundColor: backgroundColor,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(size / 2),
          child: Image.network(
            imageUrl!,
            width: size,
            height: size,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Center(
                child: Text(
                  name.isNotEmpty ? name[0].toUpperCase() : "",
                  // style: AppTextStyle.body1Medium.copyWith(
                  //   color: AppColors.white,
                  // ),
                ),
              );
            },
          ),
        ),
      );
    } else {
      avatarWidget = CircleAvatar(
        radius: size / 2,
        backgroundColor: backgroundColor,
        child: Text(
          name.isNotEmpty ? name[0].toUpperCase() : "",

          // style:
          //  nameStyle ??
          //    style:. AppTextStyle.body1Medium.copyWith(color: AppColors.white),
        ),
      );
    }

    return avatarWidget;
  }
}
