import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:toii_social/widget/colors/colors.dart';

class CachedNetworkImageWidget extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final Color? placeholderColor;
  final Color? errorColor;
  final Widget? errorWidget;
  final Widget? placeholderWidget;
  final bool showShimmer;

  const CachedNetworkImageWidget({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.placeholderColor,
    this.errorColor,
    this.errorWidget,
    this.placeholderWidget,
    this.showShimmer = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.themeData;

    Widget imageWidget = CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      placeholder:
          placeholderWidget != null
              ? (context, url) => placeholderWidget!
              : showShimmer
              ? (context, url) => Shimmer.fromColors(
                baseColor: Colors.grey.shade300,
                highlightColor: Colors.grey.shade100,
                child: Container(color: placeholderColor ?? Colors.white),
              )
              : null,
      errorWidget:
          errorWidget != null
              ? (context, url, error) => errorWidget!
              : (context, url, error) => Container(
                color: errorColor ?? theme.neutral200,
                child: const Icon(Icons.error, color: Colors.red),
              ),
    );

    if (borderRadius != null) {
      imageWidget = ClipRRect(borderRadius: borderRadius!, child: imageWidget);
    }

    return imageWidget;
  }
}

// Convenience factory methods for common use cases
class ToiiCachedImage {
  static Widget square({
    required String imageUrl,
    required double size,
    BorderRadius? borderRadius,
    BoxFit fit = BoxFit.cover,
    bool showShimmer = true,
  }) {
    return CachedNetworkImageWidget(
      imageUrl: imageUrl,
      width: size,
      height: size,
      fit: fit,
      borderRadius: borderRadius,
      showShimmer: showShimmer,
    );
  }

  static Widget aspectRatio({
    required String imageUrl,
    required double aspectRatio,
    BorderRadius? borderRadius,
    BoxFit fit = BoxFit.cover,
    bool showShimmer = true,
  }) {
    return AspectRatio(
      aspectRatio: aspectRatio,
      child: CachedNetworkImageWidget(
        imageUrl: imageUrl,
        fit: fit,
        borderRadius: borderRadius,
        showShimmer: showShimmer,
      ),
    );
  }

  static Widget flexible({
    required String imageUrl,
    BorderRadius? borderRadius,
    BoxFit fit = BoxFit.cover,
    bool showShimmer = true,
  }) {
    return CachedNetworkImageWidget(
      imageUrl: imageUrl,
      fit: fit,
      borderRadius: borderRadius,
      showShimmer: showShimmer,
    );
  }
}
