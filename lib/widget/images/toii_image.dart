import 'dart:io';
import 'dart:typed_data';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class ToiiImageProvider {
  ///  [maxWidth] và [maxHeight] được sử dụng để chỉ định kích thước tối đa của hình ảnh được tải vào từ network trước khi lưu vào bộ nhớ cache.
  ///  Điều này giúp hạn chế kích thước của file ảnh trên disk và giảm việc sử dụng băng thông mạng.
  ///  Chúng không trực tiếp ảnh hưởng đến kích thước mà hình ảnh sẽ hiển thị trong UI, nhưng hạn chế kích thước lưu trữ.
  ///  [maxWidth] và [maxHeight] nên đư<PERSON><PERSON> sử dụng khi bạn muốn giới hạn kích thước tải về từ mạng,
  ///  đặc biệt là khi bạn biết rằng hình ảnh sẽ không bao giờ cần phải được hiển thị ở kích thước lớn hơn trên thiết bị.
  ///

  /// [memCacheWidth] và [memCacheHeight] được sử dụng để chỉ định kích thước tối đa mà hình ảnh có thể có khi lưu trữ trong bộ nhớ cache của RAM.
  /// Chúng ảnh hưởng đến bộ nhớ đệm trong RAM, đó là nơi đối tượng hình ảnh tồn tại khi đang được hiển thị hoặc chờ để hiển thị trên UI.
  /// [memCacheWidth] và [memCacheHeight] nên được sử dụng khi bạn muốn kiểm soát lượng RAM được sử dụng bởi hình ảnh,
  /// điều này rất quan trọng đối với các thiết bị có RAM hạn chế.
  ///
 
  static ImageProvider<Object> fromCachedNetworkImage({
    required String imageUrl,
    String? cacheKey,
    double scale = 1.0,
    int? maxWidth,
    int? maxHeight,
    int? memCacheWidth,
    int? memCacheHeight,
  }) {
    return ResizeImage.resizeIfNeeded(
      memCacheWidth,
      memCacheHeight,
      CachedNetworkImageProvider(
        imageUrl,
        cacheKey: cacheKey,
        scale: scale,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      ),
    );
  }

  static ImageProvider<Object> fromNetwork({
    required String src,
    double scale = 1.0,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return ResizeImage.resizeIfNeeded(
      cacheWidth,
      cacheHeight,
      NetworkImage(src, scale: scale),
    );
  }

  static ImageProvider<Object> fromFile({
    required File file,
    double scale = 1.0,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return ResizeImage.resizeIfNeeded(
      cacheWidth,
      cacheHeight,
      FileImage(file, scale: scale),
    );
  }

  static ImageProvider<Object> fromAsset({
    required String name,
    AssetBundle? bundle,
    double? scale,
    String? package,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return ResizeImage.resizeIfNeeded(
      cacheWidth,
      cacheHeight,
      scale != null
          ? ExactAssetImage(
              name,
              bundle: bundle,
              scale: scale,
              package: package,
            )
          : AssetImage(name, bundle: bundle, package: package),
    );
  }

  static ImageProvider<Object> fromMemory({
    required Uint8List bytes,
    double scale = 1.0,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return ResizeImage.resizeIfNeeded(
      cacheWidth,
      cacheHeight,
      MemoryImage(bytes, scale: scale),
    );
  }
}

class ToiiImage extends StatefulWidget {
  final bool evictWhenDispose;
  final BoxShape shape;
  final BorderRadiusGeometry? borderRadius;
  final Color? backgroundColor;
  final CachedNetworkImage? network;
  final Image? file;
  final Image? asset;
  final Image? memory;

  static clearImageCache() {
    imageCache.clearLiveImages();
    imageCache.clear();
  }

  const ToiiImage._({
    super.key,
    this.evictWhenDispose = false,
    this.shape = BoxShape.rectangle,
    this.borderRadius,
    this.backgroundColor,
    this.network,
    this.file,
    this.asset,
    this.memory,
  });

  factory ToiiImage.fromNetwork({
    required CachedNetworkImage network,
    bool evictWhenDispose = false,
    BoxShape shape = BoxShape.rectangle,
    BorderRadiusGeometry? borderRadius,
    Color? backgroundColor,
    Key? key,
  }) {
    return ToiiImage._(
      key: key,
      evictWhenDispose: evictWhenDispose,
      shape: shape,
      borderRadius: borderRadius,
      backgroundColor: backgroundColor,
      network: network,
    );
  }

  factory ToiiImage.fromFile({
    required Image file,
    bool evictWhenDispose = false,
    BoxShape shape = BoxShape.rectangle,
    BorderRadiusGeometry? borderRadius,
    Color? backgroundColor,
    Key? key,
  }) {
    return ToiiImage._(
      key: key,
      evictWhenDispose: evictWhenDispose,
      shape: shape,
      borderRadius: borderRadius,
      backgroundColor: backgroundColor,
      file: file,
    );
  }

  factory ToiiImage.fromAsset({
    required Image asset,
    bool evictWhenDispose = false,
    BoxShape shape = BoxShape.rectangle,
    BorderRadiusGeometry? borderRadius,
    Color? backgroundColor,
    Key? key,
  }) {
    return ToiiImage._(
      key: key,
      evictWhenDispose: evictWhenDispose,
      shape: shape,
      borderRadius: borderRadius,
      backgroundColor: backgroundColor,
      asset: asset,
    );
  }

  factory ToiiImage.fromMemory({
    required Image memory,
    bool evictWhenDispose = false,
    BoxShape shape = BoxShape.rectangle,
    BorderRadiusGeometry? borderRadius,
    Color? backgroundColor,
    Key? key,
  }) {
    return ToiiImage._(
      key: key,
      evictWhenDispose: evictWhenDispose,
      shape: shape,
      borderRadius: borderRadius,
      backgroundColor: backgroundColor,
      memory: memory,
    );
  }

  @override
  State<ToiiImage> createState() => _ToiiImageState();
}

class _ToiiImageState extends State<ToiiImage> {
  @override
  void dispose() {
    if (widget.evictWhenDispose) {
      final network = widget.network;
      final file = widget.file;
      final asset = widget.asset;
      final memory = widget.memory;
      ImageProvider<Object>? imageProvider;
      if (network != null) {
        imageProvider = ToiiImageProvider.fromCachedNetworkImage(
          imageUrl: network.imageUrl,
          maxWidth: network.maxWidthDiskCache,
          maxHeight: network.maxHeightDiskCache,
          memCacheWidth: network.memCacheWidth,
          memCacheHeight: network.memCacheHeight,
        );
      } else {
        imageProvider = (file ?? asset ?? memory)?.image;
      }
      if (imageProvider != null) {
        imageProvider.evict().then((value) {
          // logger.e("MMImage: evictWhenDispose = $value");
        });
      }
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final backgroundColor = widget.backgroundColor;
    final network = widget.network;
    final file = widget.file;
    final asset = widget.asset;
    final memory = widget.memory;
    var child = network ?? file ?? asset ?? memory;
    if (child == null) {
      throw Exception("Child can not null!");
    }
    if (backgroundColor != null) {
      child = ColoredBox(color: backgroundColor, child: child);
    }
    if (widget.shape == BoxShape.circle) {
      return ClipOval(
        child: child,
      );
    } else {
      final borderRadius = widget.borderRadius;
      if (borderRadius != null) {
        return ClipRRect(
          borderRadius: borderRadius,
          child: child,
        );
      } else {
        return child;
      }
    }
  }
}
