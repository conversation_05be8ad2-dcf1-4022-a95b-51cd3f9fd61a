import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class BaseAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool centerTitle;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double elevation;
  final bool automaticallyImplyLeading;
  final VoidCallback? onBackPressed;
  final Widget? widgetTitle;
  final Widget? flexibleSpace;
  final PreferredSizeWidget? bottom;
  final double? heightAppbar;
  final int? maxLines;
  final TextStyle? styleTitle;
  final double? leadingWidth;
  final bool isTopLeadingIcon;

  const BaseAppBar({
    super.key,
    this.title,
    this.actions,
    this.leading,
    this.centerTitle = false,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation = 0,
    this.automaticallyImplyLeading = true,
    this.onBackPressed,
    this.widgetTitle,
    this.flexibleSpace,
    this.bottom,
    this.heightAppbar,
    this.maxLines,
    this.styleTitle,
    this.leadingWidth,
    this.isTopLeadingIcon = false,
  });

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;
    return AppBar(
      surfaceTintColor: Colors.transparent,
      title:
          widgetTitle ??
          (title != null
              ? Text(
                title!,
                style:
                    styleTitle ??
                    titleLarge.copyWith(color: themeData.neutral800),
                textAlign: TextAlign.center,
                maxLines: maxLines ?? 1,
                overflow: TextOverflow.ellipsis,
              )
              : null),
      centerTitle: centerTitle,
      backgroundColor: backgroundColor ?? themeData.neutral50,
      elevation: elevation,
      toolbarHeight: heightAppbar,
      titleSpacing: 0,
      automaticallyImplyLeading: automaticallyImplyLeading,
      leading: leading ?? _buildDefaultLeading(context),
      leadingWidth: leadingWidth ?? 70,
      actions: actions,
      flexibleSpace: flexibleSpace,
      bottom: bottom,
      iconTheme: IconThemeData(
        color: foregroundColor ?? themeData.textPrimary,
        size: 24,
      ),
      systemOverlayStyle: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
      ),
    );
  }

  Widget? _buildDefaultLeading(BuildContext context) {
    if (!automaticallyImplyLeading) return null;
    return Row(
      children: [
        SizedBox(width: 8),
        Container(
          margin: const EdgeInsets.all(8),
          child: Material(
            color: context.themeData.neutral100,
            borderRadius: BorderRadius.circular(16),
            child: InkWell(
              borderRadius: BorderRadius.circular(8),
              onTap: onBackPressed ?? () => Navigator.of(context).pop(),
              child: SizedBox(
                width: 40,
                height: 40,
                child: Icon(
                  Icons.chevron_left,
                  color: context.themeData.textPrimary,
                  size: 24,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(heightAppbar ?? kToolbarHeight);
}
