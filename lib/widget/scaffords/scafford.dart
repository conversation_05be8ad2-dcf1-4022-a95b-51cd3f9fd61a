import 'package:flutter/material.dart';

class BaseScaffold extends StatelessWidget {
  final Widget title;
  final Widget body;
  final Widget? bottom;
  final PreferredSizeWidget? appbar;
  final List<Widget> actions;
  final VoidCallback? onBackPressed;
  final bool showLeading;
  final bool? resizeToAvoidBottomInset;
  final Widget? floatingActionButton;
  final bool? automaticallyImplyLeading;
  final Widget? background;
  final bool extendBodyBehindAppBar;
  const BaseScaffold({
    super.key,
    required this.title,
    required this.body,
    this.bottom,
    this.appbar,
    this.actions = const [],
    this.onBackPressed,
    this.showLeading = false,
    this.resizeToAvoidBottomInset,
    this.floatingActionButton,
    this.automaticallyImplyLeading,
    this.background,
    this.extendBodyBehindAppBar = false,
  });

  @override
  Widget build(BuildContext context) {
    Widget scaffoldBody = body;
    if (background != null) {
      scaffoldBody = Stack(
        children: [
          Positioned.fill(child: background!),
          Positioned.fill(child: body),
        ],
      );
    }
    return Scaffold(
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      extendBodyBehindAppBar: extendBodyBehindAppBar,
      backgroundColor: Colors.white,
      appBar:
          appbar ??
          AppBar(
            automaticallyImplyLeading: automaticallyImplyLeading ?? true,
            leading:
                showLeading
                    ? IconButton(
                      icon: Icon(Icons.arrow_back, color: Colors.black),
                      onPressed: () {
                        if (onBackPressed != null) {
                          onBackPressed!();
                        } else {
                          Navigator.of(context).pop();
                        }
                      },
                    )
                    : null,
            backgroundColor: Colors.transparent,
            title: title,
            actions: actions,
          ),
      body: scaffoldBody,
      bottomNavigationBar: bottom,
      floatingActionButton: floatingActionButton,
    );
  }
}
