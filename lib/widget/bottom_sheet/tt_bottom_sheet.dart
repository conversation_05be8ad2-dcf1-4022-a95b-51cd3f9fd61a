import 'package:flutter/material.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/widget/bottom_sheet/base_bottom_sheet.dart';
import 'package:toii_social/widget/colors/colors.dart';

Future<dynamic> showTtBottomSheet(
  BuildContext context, {
  bool isDismissible = true,
  String? title,
  required Widget child,
  bool isShowClose = false,
  EdgeInsetsGeometry? padding,
}) {
  return showModalBottomSheet(
    context: context,
    backgroundColor: Colors.transparent,
    elevation: 0,
    enableDrag: false,
    isDismissible: isDismissible,
    isScrollControlled: true,
    builder:
        (context) => Container(
          decoration: BoxDecoration(
            color: themeData.schemesOnPrimary,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
          ),
          child: Wrap(
            children: [
              BaseBottomSheet(
                isShowClose: isShowClose,
                title: title,
                padding: padding,
                child: child,
              ),
            ],
          ),
        ),
  );
}
