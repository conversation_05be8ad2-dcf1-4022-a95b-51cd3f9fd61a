import 'package:flutter/material.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/widget/colors/colors.dart';

class PullToRefreshView extends StatelessWidget {
  const PullToRefreshView({
    super.key,
    required this.child,
    required this.onRefresh,
  });

  final Widget child;

  final RefreshCallback onRefresh;

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: onRefresh,
      color: themeData.primaryGreen500,
      child: child,
    );
  }
}
