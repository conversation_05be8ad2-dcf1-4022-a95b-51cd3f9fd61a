import 'package:flutter/material.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class CustomTabBar extends StatefulWidget {
  final TabController controller;
  final List<String> tabs;
  final Color? selectedColor;
  final Color? unselectedColor;
  final double? fontSize;
  final FontWeight? selectedFontWeight;
  final FontWeight? unselectedFontWeight;
  final double? indicatorWidth;
  final EdgeInsets? padding;
  final double? indicatorThickness;
  final double? unselectedOpacity; // Thêm opacity cho unselected color

  const CustomTabBar({
    super.key,
    required this.controller,
    required this.tabs,
    this.selectedColor,
    this.unselectedColor,
    this.fontSize,
    this.selectedFontWeight,
    this.unselectedFontWeight,
    this.indicatorWidth,
    this.padding,
    this.indicatorThickness,
    this.unselectedOpacity,
  });

  @override
  State<CustomTabBar> createState() => _CustomTabBarState();
}

class _CustomTabBarState extends State<CustomTabBar> {
  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onTabChanged);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTabChanged);
    super.dispose();
  }

  void _onTabChanged() {
    setState(() {
      // Trigger rebuild when tab changes
    });
  }

  @override
  Widget build(BuildContext context) {
    final defaultSelectedColor = const Color(0xff82AD0A);
    final defaultUnselectedColor = Theme.of(
      context,
    ).colorScheme.onSurface.withOpacity(widget.unselectedOpacity ?? 0.4);

    return Container(
      padding: widget.padding ?? const EdgeInsets.symmetric(horizontal: 16),
      child: TabBar(
        controller: widget.controller,
        tabs:
            widget.tabs.asMap().entries.map((entry) {
              final index = entry.key;
              final tab = entry.value;
              final isSelected = index == widget.controller.index;

              return Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                child: Text(
                  tab,
                  style:
                      isSelected
                          ? titleLarge.copyWith(
                            color: widget.selectedColor ?? defaultSelectedColor,
                          )
                          : titleMedium.copyWith(
                            color:
                                widget.unselectedColor ??
                                defaultUnselectedColor,
                          ),
                ),
              );
            }).toList(),
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(
            width: widget.indicatorThickness ?? 2.0,
            color: defaultSelectedColor,
          ),
          insets: const EdgeInsets.symmetric(horizontal: 0),
        ),
        indicatorSize: TabBarIndicatorSize.label,
        dividerColor: Colors.transparent,
        labelPadding: const EdgeInsets.only(right: 32),
        overlayColor: WidgetStateProperty.all(Colors.transparent),
      ),
    );
  }
}
