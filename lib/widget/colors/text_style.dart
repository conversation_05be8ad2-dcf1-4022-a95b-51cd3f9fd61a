import 'package:flutter/material.dart';

const Color _defaultTextColor = Color.fromARGB(255, 99, 86, 86);
const String _fontFamily = "Manrope";

// Display
const TextStyle displayLarge = TextStyle(
  fontFamily: _fontFamily,
  fontSize: 64,
  height: 84 / 64,
  fontWeight: FontWeight.w500,
  letterSpacing: -0.25,
  color: _defaultTextColor,
);

const TextStyle displayMedium = TextStyle(
  fontFamily: _fontFamily,
  fontSize: 45,
  height: 52 / 45,
  fontWeight: FontWeight.w500,
  letterSpacing: 0,
  color: _defaultTextColor,
);

const TextStyle displaySmall = TextStyle(
  fontFamily: _fontFamily,
  fontSize: 36,
  height: 44 / 36,
  fontWeight: FontWeight.w500,
  letterSpacing: 0,
  color: _defaultTextColor,
);

// Headlines
const TextStyle headlineLarge = TextStyle(
  fontFamily: _fontFamily,
  fontSize: 32,
  height: 40 / 32,
  fontWeight: FontWeight.w700,
  color: _defaultTextColor,
);

const TextStyle headlineMedium = TextStyle(
  fontFamily: _fontFamily,
  fontSize: 28,
  height: 36 / 28,
  fontWeight: FontWeight.w700,
  color: _defaultTextColor,
);

const TextStyle headlineSmall = TextStyle(
  fontFamily: _fontFamily,
  fontSize: 24,
  height: 32 / 24,
  fontWeight: FontWeight.w700,
  color: _defaultTextColor,
);

// Titles
const TextStyle titleLarge = TextStyle(
  fontFamily: _fontFamily,
  fontSize: 20,
  height: 28 / 20,
  fontWeight: FontWeight.w600,
  color: _defaultTextColor,
);

const TextStyle titleMedium = TextStyle(
  fontFamily: _fontFamily,
  fontSize: 16,
  height: 24 / 16,
  fontWeight: FontWeight.w600,
  color: _defaultTextColor,
);

const TextStyle titleSmall = TextStyle(
  fontFamily: _fontFamily,
  fontSize: 14,
  height: 20 / 14,
  fontWeight: FontWeight.w600,
  color: _defaultTextColor,
);

// Labels
const TextStyle labelLarge = TextStyle(
  fontFamily: _fontFamily,
  fontSize: 14,
  height: 20 / 14,
  fontWeight: FontWeight.w600,
  color: _defaultTextColor,
);

const TextStyle labelMedium = TextStyle(
  fontFamily: _fontFamily,
  fontSize: 12,
  height: 16 / 12,
  fontWeight: FontWeight.w600,
  color: _defaultTextColor,
);

const TextStyle labelSmall = TextStyle(
  fontFamily: _fontFamily,
  fontSize: 11,
  height: 16 / 11,
  fontWeight: FontWeight.w600,
  color: _defaultTextColor,
);

// Body
const TextStyle bodyLarge = TextStyle(
  fontFamily: _fontFamily,
  fontSize: 16,
  height: 24 / 16,
  fontWeight: FontWeight.w400,
  color: _defaultTextColor,
);

const TextStyle bodyMedium = TextStyle(
  fontFamily: _fontFamily,
  fontSize: 14,
  height: 20 / 14,
  fontWeight: FontWeight.w400,
  color: _defaultTextColor,
);

const TextStyle bodySmall = TextStyle(
  fontFamily: _fontFamily,
  fontSize: 12,
  height: 16 / 12,
  fontWeight: FontWeight.w400,
  color: _defaultTextColor,
);

const TextStyle bodyXSmall = TextStyle(
  fontFamily: _fontFamily,
  fontSize: 10,
  height: 12 / 10,
  fontWeight: FontWeight.w400,
  color: _defaultTextColor,
);
