import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/chain_repository.dart';
import 'package:toii_social/model/chain/chain_model.dart';
import 'package:toii_social/model/chain/token_model.dart';
import 'package:toii_social/utils/utils.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';

class AddCustomToken extends StatefulWidget {
  final ChainModel chainModel;
  const AddCustomToken({super.key, required this.chainModel});

  @override
  State<AddCustomToken> createState() => _AddCustomTokenState();
}

class _AddCustomTokenState extends State<AddCustomToken> {
  final TextStyle propertyStyle = const TextStyle(
    fontSize: 18,
    color: Colors.grey,
  );
  final TextStyle valueStyle = const TextStyle(
    fontSize: 18,
    color: Colors.black,
    fontWeight: FontWeight.bold,
  );
  TextEditingController contractTextController = TextEditingController(
    text: "",
  );
  String tokenName = "-";
  String symbol = "-";
  String decimal = "-";
  bool isFetchingTokenInfo = false;
  bool isTokenAvailable = false;
  TokenModel? token;

  checkTokenInfo() async {
    if (!isAddress(contractTextController.text)) {
      context.showSnackbar(message: "Address not valid");
      return;
    }
    try {
      setState(() {
        isTokenAvailable = false;
        isFetchingTokenInfo = true;
      });

      TokenModel token = await GetIt.instance<ChainRepository>().fetchTokenInfo(
        contractTextController.text,
        widget.chainModel,
      );
      setState(() {
        isTokenAvailable = true;
        tokenName = token.name!;
        symbol = token.symbol!;
        decimal = token.decimal!;
        this.token = token;
        isFetchingTokenInfo = false;
      });
    } catch (_) {
      contractTextController.text = "";
      context.showSnackbar(message: "Address not found");
      setState(() {
        isTokenAvailable = false;
        isFetchingTokenInfo = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(35),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            children: [
              TextFormField(
                style: TextStyle(color: Colors.black),
                controller: contractTextController,
                decoration: const InputDecoration(
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(13)),
                    borderSide: BorderSide(color: Color(0xFF141414)),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(13)),
                    borderSide: BorderSide(color: Color(0xFF141414)),
                  ),
                  isDense: true,
                  label: Text("Contract Address"),
                ),
              ),
              const SizedBox(height: 35),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text("Name", style: propertyStyle),
                  Text(tokenName, style: valueStyle),
                ],
              ),
              const SizedBox(height: 10),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text("Symbol", style: propertyStyle),
                  Text(symbol, style: valueStyle),
                ],
              ),
              const SizedBox(height: 10),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text("Decimal", style: propertyStyle),
                  Text(decimal, style: valueStyle),
                ],
              ),
              const SizedBox(height: 60),
            ],
          ),
          isFetchingTokenInfo
              ? const Center(
                child: CircularProgressIndicator(color: Color(0xFF141414)),
              )
              : !isTokenAvailable
              ? InkWell(
                onTap: () {
                  checkTokenInfo();
                },
                child: Container(
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(vertical: 18, horizontal: 15),
                  decoration: BoxDecoration(
                    color: Color(0xFF141414),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Center(
                    child: Text(
                      "Check Token",
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              )
              : InkWell(
                onTap: () {
                  // if (token != null) {
                  //   final item = TokenItemModel(
                  //     symbol: token!.symbol,
                  //     chainId:
                  //         GetIt.instance<WalletCubit>()
                  //             .currentActiveChain
                  //             .chainId ??
                  //         "",
                  //     address: token!.contract!,
                  //     name: token!.name ?? "",
                  //   );
                  //   GetIt.instance<ChainApiRepository>().addNewCustomToken(
                  //     item,
                  //   );
                  //   // GetIt.instance<WalletCubit>().saveCustomToken(token!);
                  //   // GetIt.instance<WalletCubit>().fetchBalanceFromChain();
                  //   // GetIt.instance<WalletCubit>().fetchAllChainFromApi();
                  //   Navigator.of(context).pop(true);

                  //   GetIt.instance<NavigationService>().navigatorContext
                  //       .showSnackBarSuccess(text: "Add success");
                  // }
                },
                child: Container(
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(vertical: 18, horizontal: 15),
                  decoration: BoxDecoration(
                    color: Color(0xFF141414),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Center(
                    child: Text(
                      "Save Token",
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
        ],
      ),
    );
  }
}

Future<dynamic> showBottomSheetAddCustomToken(
  BuildContext context,
  ChainModel chainModel,
) {
  return showModalBottomSheet(
    isScrollControlled: true,
    context: context,
    builder: (BuildContext context) {
      return Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(30),
            topRight: Radius.circular(30),
          ),
        ),
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: AddCustomToken(chainModel: chainModel),
      );
    },
  );
}
