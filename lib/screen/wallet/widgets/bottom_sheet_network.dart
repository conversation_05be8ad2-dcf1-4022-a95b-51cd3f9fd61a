import 'package:flutter/material.dart';
import 'package:toii_social/core/constant/constant.dart';
import 'package:toii_social/screen/wallet/widgets/chain_item.dart';

class ChainWidget extends StatelessWidget {
  const ChainWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 35, horizontal: 20),
      height: MediaQuery.of(context).size.height * 0.4,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        // color: zinc9002,
      ),
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Select network',
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 20),
              ...DEFAULT_CHAINS.map((e) => ChainItem(e: e)),
            ],
          ),
        ),
      ),
    );
  }
}

Future<dynamic> showBottomSheetNetwork(BuildContext context) {
  return showModalBottomSheet(
    isScrollControlled: true,
    context: context,
    builder: (BuildContext modalContext) {
      return Container(
        width: double.infinity,
        decoration: const BoxDecoration(
          //    color: zinc9002,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(30),
            topRight: Radius.circular(30),
          ),
        ),
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(modalContext).viewInsets.bottom,
        ),
        child: const ChainWidget(),
      );
    },
  );
}
