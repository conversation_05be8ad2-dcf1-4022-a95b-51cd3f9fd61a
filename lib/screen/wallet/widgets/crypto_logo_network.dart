import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/widget/colors/colors.dart';

class CryptoLogoNetwork extends StatelessWidget {
  const CryptoLogoNetwork({
    required this.id,
    required this.name,
    this.size = 40,
    super.key,
  });
  final String id;
  final String name;
  final double size;

  @override
  Widget build(BuildContext context) {
    if (name.toLowerCase().contains("toii")) {
      return SizedBox(
        height: size,
        width: size,
        child: Assets.icons.icTokenLogo.image(),
      );
    }
    return SizedBox(
      width: size,
      height: size,
      child: CachedNetworkImage(
        imageUrl: "https://s2.coinmarketcap.com/static/img/coins/64x64/$id.png",
        // placeholder: (context, _) {
        //   return Image.asset("assets/coin/${name.toLowerCase()}.png");
        // },
        errorWidget: _errorIcon,
      ),
    );
    // return Image.network(
    //   IconNetwork().getIconToNetwork(id),
    //   height: 40,
    //   width: 40,
    //   placeholder: (context, _) {
    //     return Image.asset("assets/coin/${item.iconId}.png");
    //   },
    //   errorBuilder: _errorIcon,
    // );
  }

  Widget _errorIcon(context, error, stackTrace) {
    return Image.asset(
      "assets/coin/${name.toLowerCase()}.png",
      errorBuilder:
          (context, error, stackTrace) => Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(color: themeData.neutral800),
            child: Center(
              child: Text(
                name[0].toUpperCase(),
                style: const TextStyle(color: Colors.white, fontSize: 18),
              ),
            ),
          ),
    );
  }
}
