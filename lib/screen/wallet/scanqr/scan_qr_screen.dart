import 'package:flutter/material.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';

class ScanQrScreen extends StatefulWidget {
  const ScanQrScreen({super.key});

  @override
  State<ScanQrScreen> createState() => _ScanQrScreenState();
}

class _ScanQrScreenState extends State<ScanQrScreen> {
  late MobileScannerController controller;
  bool isScan = false;

  @override
  void initState() {
    super.initState();
    controller = MobileScannerController();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  void _onDetect(BarcodeCapture capture) {
    final List<Barcode> barcodes = capture.barcodes;
    if (!isScan && barcodes.isNotEmpty) {
      final barcode = barcodes.first;
      if (barcode.rawValue?.isNotEmpty ?? false) {
        isScan = true;
        Navigator.of(context).pop(barcode.rawValue ?? "");
      }
    }
  }

  Widget _buildQrView(BuildContext context) {
    var scanArea =
        (MediaQuery.of(context).size.width < 400 ||
                MediaQuery.of(context).size.height < 400)
            ? 300.0
            : 500.0;

    return Stack(
      children: [
        MobileScanner(controller: controller, onDetect: _onDetect),
        _buildScannerOverlay(scanArea),
      ],
    );
  }

  Widget _buildScannerOverlay(double scanArea) {
    return Container(
      decoration: ShapeDecoration(
        shape: ScannerOverlayShape(
          cutOutSize: scanArea,
          borderColor: themeData.primaryGreen600,
          borderRadius: 8,
          borderLength: 30,
          borderWidth: 4,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BaseScaffold(
      extendBodyBehindAppBar: true,
      title: Text("Scan QR code", style: TextStyle(color: Colors.white)),
      appbar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text("Scan QR code", style: TextStyle(color: Colors.white)),
        leading: IconButton(
          icon: Icon(Icons.close, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: ClipRRect(
        borderRadius: BorderRadius.circular(12.0),
        child: Column(children: [Expanded(child: _buildQrView(context))]),
      ),
    );
  }
}

class ScannerOverlayShape extends ShapeBorder {
  const ScannerOverlayShape({
    this.borderColor = Colors.red,
    this.borderWidth = 3.0,
    this.overlayColor = const Color.fromRGBO(0, 0, 0, 80),
    this.borderRadius = 0,
    this.borderLength = 40,
    this.cutOutSize = 250,
  });

  final Color borderColor;
  final double borderWidth;
  final Color overlayColor;
  final double borderRadius;
  final double borderLength;
  final double cutOutSize;

  @override
  EdgeInsetsGeometry get dimensions => const EdgeInsets.all(10);

  @override
  Path getInnerPath(Rect rect, {TextDirection? textDirection}) {
    return Path()
      ..fillType = PathFillType.evenOdd
      ..addPath(getOuterPath(rect), Offset.zero);
  }

  @override
  Path getOuterPath(Rect rect, {TextDirection? textDirection}) {
    Path getLeftTopPath(Rect rect) {
      return Path()
        ..moveTo(rect.left, rect.bottom)
        ..lineTo(rect.left, rect.top + borderRadius)
        ..quadraticBezierTo(
          rect.left,
          rect.top,
          rect.left + borderRadius,
          rect.top,
        )
        ..lineTo(rect.right, rect.top);
    }

    return getLeftTopPath(rect)
      ..lineTo(rect.right, rect.bottom)
      ..lineTo(rect.left, rect.bottom)
      ..lineTo(rect.left, rect.top);
  }

  @override
  void paint(Canvas canvas, Rect rect, {TextDirection? textDirection}) {
    final width = rect.width;
    final borderWidthSize = width / 2;
    final height = rect.height;
    final borderOffset = borderWidth / 2;
    final adjustedBorderLength =
        borderLength > cutOutSize / 2 + borderWidth * 2
            ? borderWidthSize / 2
            : borderLength;
    final adjustedCutOutSize =
        cutOutSize < width ? cutOutSize : width - borderOffset;

    final backgroundPaint =
        Paint()
          ..color = overlayColor
          ..style = PaintingStyle.fill;

    final borderPaint =
        Paint()
          ..color = borderColor
          ..style = PaintingStyle.stroke
          ..strokeWidth = borderWidth;

    final boxPaint =
        Paint()
          ..color = borderColor
          ..style = PaintingStyle.fill
          ..blendMode = BlendMode.dstOut;

    final cutOutRect = Rect.fromLTWH(
      rect.left + width / 2 - cutOutSize / 2 + borderOffset,
      rect.top + height / 2 - cutOutSize / 2 + borderOffset,
      cutOutSize - borderOffset * 2,
      cutOutSize - borderOffset * 2,
    );

    canvas
      ..saveLayer(rect, backgroundPaint)
      ..drawRect(rect, backgroundPaint)
      ..drawRRect(
        RRect.fromRectAndRadius(cutOutRect, Radius.circular(borderRadius)),
        boxPaint,
      )
      ..restore();

    // Draw the border
    canvas.drawRRect(
      RRect.fromRectAndRadius(cutOutRect, Radius.circular(borderRadius)),
      borderPaint,
    );

    // Draw corner lines
    final path = Path();
    // Top left corner
    path.moveTo(cutOutRect.left - borderOffset, cutOutRect.top + borderLength);
    path.lineTo(cutOutRect.left - borderOffset, cutOutRect.top + borderRadius);
    path.quadraticBezierTo(
      cutOutRect.left - borderOffset,
      cutOutRect.top - borderOffset,
      cutOutRect.left + borderRadius,
      cutOutRect.top - borderOffset,
    );
    path.lineTo(cutOutRect.left + borderLength, cutOutRect.top - borderOffset);

    // Top right corner
    path.moveTo(cutOutRect.right - borderLength, cutOutRect.top - borderOffset);
    path.lineTo(cutOutRect.right - borderRadius, cutOutRect.top - borderOffset);
    path.quadraticBezierTo(
      cutOutRect.right + borderOffset,
      cutOutRect.top - borderOffset,
      cutOutRect.right + borderOffset,
      cutOutRect.top + borderRadius,
    );
    path.lineTo(cutOutRect.right + borderOffset, cutOutRect.top + borderLength);

    // Bottom right corner
    path.moveTo(
      cutOutRect.right + borderOffset,
      cutOutRect.bottom - borderLength,
    );
    path.lineTo(
      cutOutRect.right + borderOffset,
      cutOutRect.bottom - borderRadius,
    );
    path.quadraticBezierTo(
      cutOutRect.right + borderOffset,
      cutOutRect.bottom + borderOffset,
      cutOutRect.right - borderRadius,
      cutOutRect.bottom + borderOffset,
    );
    path.lineTo(
      cutOutRect.right - borderLength,
      cutOutRect.bottom + borderOffset,
    );

    // Bottom left corner
    path.moveTo(
      cutOutRect.left + borderLength,
      cutOutRect.bottom + borderOffset,
    );
    path.lineTo(
      cutOutRect.left + borderRadius,
      cutOutRect.bottom + borderOffset,
    );
    path.quadraticBezierTo(
      cutOutRect.left - borderOffset,
      cutOutRect.bottom + borderOffset,
      cutOutRect.left - borderOffset,
      cutOutRect.bottom - borderRadius,
    );
    path.lineTo(
      cutOutRect.left - borderOffset,
      cutOutRect.bottom - borderLength,
    );

    canvas.drawPath(path, borderPaint..strokeWidth = borderWidth * 2);
  }

  @override
  ShapeBorder scale(double t) {
    return ScannerOverlayShape(
      borderColor: borderColor,
      borderWidth: borderWidth,
      overlayColor: overlayColor,
    );
  }
}
