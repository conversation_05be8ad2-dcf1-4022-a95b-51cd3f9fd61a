import 'package:flutter/material.dart';

class TransactionInfo extends StatelessWidget {
  const TransactionInfo({super.key, required this.transactionId});

  final String transactionId;
  //final String explorerUrl;

  @override
  Widget build(BuildContext context) {
    // final url = useMemoized(() => Uri.parse('$explorerUrl/tx/$transactionId'),
    //     [explorerUrl, transactionId]);
    final theme = Theme.of(context);

    return InkWell(
        child: Text(
            'View transaction ${EthAddressFormatter(transactionId).mask()}',
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.red)),
        onTap: () async {
          // if (await canLaunchUrl(url)) {
          //   await launchUrl(url);
          // }
        });
  }
}

class EthAddressFormatter {
  EthAddressFormatter(this.address);
  final String address;
  String mask() {
    return '${address.substring(0, 6)}...${address.substring(address.length - 6, address.length)}';
  }
}
