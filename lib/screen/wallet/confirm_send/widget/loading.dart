import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class Loading extends StatelessWidget {
  const Loading({super.key});

  @override
  Widget build(BuildContext context) {
    return const Material(
      type: MaterialType.transparency,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'Your transaction is being processed',
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.white),
          ),
          <PERSON><PERSON><PERSON><PERSON>(height: 48.0),
          SpinKitWanderingCubes(color: Colors.blue),
          <PERSON><PERSON><PERSON><PERSON>(height: 48.0),
        ],
      ),
    );
  }
}
