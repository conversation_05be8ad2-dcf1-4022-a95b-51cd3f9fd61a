import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';

class SelectTokenScreen extends StatefulWidget {
  const SelectTokenScreen({super.key});

  @override
  State<SelectTokenScreen> createState() => _SelectTokenScreenState();
}

class _SelectTokenScreenState extends State<SelectTokenScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedNetwork = 'All Networks';
  List<TokenItem> _filteredTokens = [];

  final List<TokenItem> _allTokens = [
    TokenItem(
      name: 'TOII',
      symbol: 'Toii',
      balance: '0,0034',
      balanceUsd: '\$145.78',
      icon: Icons.token,
      color: const Color(0xFF4ADE80),
    ),
    TokenItem(
      name: 'ETH',
      symbol: 'Ether',
      balance: '0',
      balanceUsd: '\$0',
      icon: Icons.currency_bitcoin,
      color: const Color(0xFF627EEA),
    ),
    TokenItem(
      name: 'BNB',
      symbol: 'BNB Chain Native Tocken',
      balance: '0',
      balanceUsd: '\$0',
      icon: Icons.token,
      color: const Color(0xFFF0B90B),
    ),
    TokenItem(
      name: 'DOGE',
      symbol: 'Doge',
      balance: '0',
      balanceUsd: '\$0',
      icon: Icons.pets,
      color: const Color(0xFFC2A633),
    ),
    TokenItem(
      name: 'Solana',
      symbol: 'Solana',
      balance: '0',
      balanceUsd: '\$0',
      icon: Icons.grain,
      color: const Color(0xFF9945FF),
    ),
    TokenItem(
      name: 'BABY',
      symbol: 'Baby',
      balance: '0',
      balanceUsd: '\$0',
      icon: Icons.child_care,
      color: const Color(0xFFFF6B35),
    ),
    TokenItem(
      name: 'USDC',
      symbol: 'Usdc',
      balance: '0',
      balanceUsd: '\$0',
      icon: Icons.monetization_on,
      color: const Color(0xFF2775CA),
    ),
    TokenItem(
      name: 'USDT',
      symbol: 'Usdt',
      balance: '0',
      balanceUsd: '\$0',
      icon: Icons.attach_money,
      color: const Color(0xFF26A17B),
    ),
    TokenItem(
      name: 'DOGE',
      symbol: 'Doge',
      balance: '0',
      balanceUsd: '\$0',
      icon: Icons.pets,
      color: const Color(0xFFC2A633),
    ),
  ];

  final List<String> _networks = [
    'All Networks',
    'Ethereum',
    'BNB Chain',
    'Polygon',
    'Solana',
  ];

  @override
  void initState() {
    super.initState();
    _filteredTokens = _allTokens;
    _searchController.addListener(_filterTokens);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterTokens() {
    setState(() {
      _filteredTokens =
          _allTokens.where((token) {
            return token.name.toLowerCase().contains(
                  _searchController.text.toLowerCase(),
                ) ||
                token.symbol.toLowerCase().contains(
                  _searchController.text.toLowerCase(),
                );
          }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BaseScaffold(
      showLeading: true,
      title: Text(
        'Select Tokens',
        style: titleLarge.copyColor(theme.neutral800),
      ),

      body: Column(
        children: [
          const SizedBox(height: 16),
          // Search bar
          _buildSearchBar(context),

          const SizedBox(height: 16),

          // Network selector
          _buildNetworkSelector(context),

          const SizedBox(height: 16),

          // Token list
          Expanded(child: _buildTokenList(context)),
        ],
      ),
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: themeData.black50,
        borderRadius: BorderRadius.circular(100),
      ),
      child: Row(
        children: [
          SvgPicture.asset(
            Assets.icons.icSearch.path,
            colorFilter: ColorFilter.mode(
              themeData.neutral400,
              BlendMode.srcIn,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: TextField(
              controller: _searchController,
              style: bodyMedium.copyWith(color: themeData.textPrimary),
              decoration: InputDecoration(
                hintText: 'Search token symbol',
                hintStyle: bodyMedium.copyWith(color: themeData.textSecondary),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNetworkSelector(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Icon(Icons.language, color: theme.textSecondary, size: 20),
          const SizedBox(width: 8),
          GestureDetector(
            onTap: () => _showNetworkSelector(context),
            child: Row(
              children: [
                Text(
                  _selectedNetwork,
                  style: bodyMedium.copyWith(
                    color: theme.textPrimary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(width: 4),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: theme.textSecondary,
                  size: 16,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTokenList(BuildContext context) {
    final theme = Theme.of(context);

    return ListView.separated(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _filteredTokens.length,
      separatorBuilder:
          (context, index) => Container(
            height: 1,
            color: theme.neutral200,
            margin: const EdgeInsets.symmetric(vertical: 8),
          ),
      itemBuilder: (context, index) {
        final token = _filteredTokens[index];
        return _buildTokenItem(context, token);
      },
    );
  }

  Widget _buildTokenItem(BuildContext context, TokenItem token) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: () => _selectToken(context, token),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            // Token icon
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: token.color,
                shape: BoxShape.circle,
              ),
              child: Icon(token.icon, color: Colors.white, size: 20),
            ),

            const SizedBox(width: 12),

            // Token info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    token.name,
                    style: bodyMedium.copyWith(
                      color: theme.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    token.symbol,
                    style: bodySmall.copyWith(color: theme.textSecondary),
                  ),
                ],
              ),
            ),

            // Token balance
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  token.balance,
                  style: bodyMedium.copyWith(
                    color: theme.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  token.balanceUsd,
                  style: bodySmall.copyWith(color: theme.textSecondary),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showNetworkSelector(BuildContext context) {
    final theme = Theme.of(context);

    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: theme.neutral300,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 20),
                Text(
                  'Select Network',
                  style: titleLarge.copyWith(
                    color: theme.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 20),
                ...(_networks.map(
                  (network) => ListTile(
                    title: Text(
                      network,
                      style: bodyMedium.copyWith(color: theme.textPrimary),
                    ),
                    leading: Icon(Icons.language, color: theme.textSecondary),
                    trailing:
                        _selectedNetwork == network
                            ? Icon(Icons.check, color: theme.primaryGreen500)
                            : null,
                    onTap: () {
                      setState(() {
                        _selectedNetwork = network;
                      });
                      Navigator.pop(context);
                    },
                  ),
                )),
                const SizedBox(height: 20),
              ],
            ),
          ),
    );
  }

  void _selectToken(BuildContext context, TokenItem token) {
    // Return selected token to previous screen
    Navigator.of(context).pop(token);
  }
}

class TokenItem {
  final String name;
  final String symbol;
  final String balance;
  final String balanceUsd;
  final IconData icon;
  final Color color;

  TokenItem({
    required this.name,
    required this.symbol,
    required this.balance,
    required this.balanceUsd,
    required this.icon,
    required this.color,
  });
}
