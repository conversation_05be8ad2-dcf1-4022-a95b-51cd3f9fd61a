import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
// import 'package:flutter_nearby_connections/flutter_nearby_connections.dart';
import 'package:flutter_styled_toast/flutter_styled_toast.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/chat/lib/domain/app_service.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';

import 'lib/presentation/app.dart';

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  Widget _buildBody() {
    return Column(
      children: [
        InkWell(
          onTap: () {
            goToChat();
          },
          child: Text(
            'gotoChat',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return BaseScaffold(title: Text("Profile"), body: _buildBody());
  }

  void goToChat() async {
    final service = AppService()..init();
    await service.getPlatformInfo();
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => App(service: service)),
    );
  }
}

