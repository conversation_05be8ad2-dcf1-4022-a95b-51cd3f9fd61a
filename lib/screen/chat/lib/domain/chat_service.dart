import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:nearby_service/nearby_service.dart';
import 'package:toii_social/screen/chat/lib/domain/app_state.dart';

class ChatService {
  late final _nearbyService = NearbyService.getInstance();

  AppState state = AppState.idle;

  String platformModel = 'Unknown';
  String platformVersion = 'Unknown';
  bool _isDarwinBrowser = true;
  NearbyDeviceInfo? currentDeviceInfo;
  List<NearbyDevice>? peers;

  StreamSubscription? _connectionInfoSubscription;
  CommunicationChannelState _communicationChannelState =
      CommunicationChannelState.notConnected;

  StreamSubscription? _peersSubscription;

  init() async {
    await getPlatformInfo();
    getSavedDarwinDeviceName().then((value) {
      initialize(value);
    });
  }

  Future<void> getPlatformInfo() async {
    platformVersion = await _nearbyService.getPlatformVersion() ?? 'Unknown';
    platformModel = await _nearbyService.getPlatformModel() ?? 'Unknown';
  }

  Future<String> getSavedDarwinDeviceName() async {
    return (await _nearbyService.darwin?.getSavedDeviceName()) ?? platformModel;
  }

  Future<void> initialize(String? darwinDeviceName) async {
    try {
      await _nearbyService.initialize(
        data: NearbyInitializeData(darwinDeviceName: darwinDeviceName),
      );
      _nearbyService.darwin?.getIsBrowserStream().listen((event) {
        _isDarwinBrowser = event;
      });
      startListeningCommunicationChannelState();

      state =
          Platform.isAndroid ? AppState.permissions : AppState.selectClientType;

      setIsBrowser(value: false);
      state = AppState.readyToDiscover;
      discover();
    } catch (e, s) {
      _log(e, s);
    } finally {}
  }

  void setIsBrowser({required bool value}) {
    _nearbyService.darwin?.setIsBrowser(value: value);
  }

  Future<void> discover() async {
    try {
      await getCurrentDeviceInfo();
      final hasRunning = await hasRunningJobs();
      if (hasRunning) {
        state = AppState.discoveringPeers;
      } else {
        final result = await _nearbyService.discover();
        if (result) {
          state = AppState.discoveringPeers;
          startListeningPeers();
        }
      }
    } on NearbyServiceBusyException catch (_) {
      _logBusyException();
    } catch (e, s) {
      _log(e, s);
    }
  }

  void startListeningCommunicationChannelState() {
    try {
      _connectionInfoSubscription = _nearbyService
          .getCommunicationChannelStateStream()
          .listen((event) async {
            _communicationChannelState = event;
          });
    } catch (e, s) {
      _log(e, s);
    }
  }

  Future<void> getCurrentDeviceInfo() async {
    try {
      currentDeviceInfo = await _nearbyService.getCurrentDeviceInfo();
    } catch (e, s) {
      _log(e, s);
    }
  }

  Future<bool> hasRunningJobs() async {
    try {
      final result = await _nearbyService.getPeers();
      // if one of devices is connecting, service is busy (android only)
      if (result.any(
        (element) => element.status == NearbyDeviceStatus.connecting,
      )) {
        if (kDebugMode) {
          print('Service has already running jobs');
        }
        return true;
      }
      return false;
    } catch (e, s) {
      _log(e, s);
      return false;
    }
  }

  Future<void> startListeningPeers() async {
    try {
      _peersSubscription = _nearbyService.getPeersStream().listen((event) {
        
        if (event.isNotEmpty) {
          print("chuan cmnrnnr");

        }
        peers = event;
      });
      state = AppState.streamingPeers;
    } catch (e, s) {
      _log(e, s);
    }
  }
}

extension LoggingExtension on ChatService {
  void _log(e, StackTrace s) {
    if (kDebugMode) {
      print('$e, \nStacktrace: $s');
    }
  }

  void _logBusyException() {
    if (kDebugMode) {
      print(
        'Nearby service is busy, wait a little and retry (You can implement retry in your code)',
      );
    }
  }
}
