import 'package:flutter/material.dart';
import 'package:toii_social/screen/chat/lib/domain/app_service.dart';
import 'package:toii_social/screen/chat/lib/uikit/uikit.dart';
import 'package:provider/provider.dart';

class DiscoveryView extends StatelessWidget {
  const DiscoveryView({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ActionButton(
          onTap: context.read<AppService>().startListeningPeers,
          title: 'Tap to get peers!',
        ),
        const Si<PERSON><PERSON>ox(height: 10),
        ActionButton(
          type: ActionType.warning,
          onTap: context.read<AppService>().stopDiscovery,
          title: 'Stop discovery',
        ),
      ],
    );
  }
}
