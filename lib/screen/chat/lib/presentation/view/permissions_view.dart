import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:toii_social/screen/chat/lib/domain/app_service.dart';
import 'package:toii_social/screen/chat/lib/uikit/uikit.dart';

class PermissionsView extends StatelessWidget {
  const PermissionsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ActionButton(
          onTap: context.read<AppService>().requestPermissions,
          title: 'Request permissions',
        ),
      ],
    );
  }
}
