import 'package:flutter/material.dart';
import 'package:toii_social/screen/chat/lib/domain/app_state.dart';
import 'package:toii_social/screen/chat/lib/presentation/view/check_service_view.dart';
import 'package:toii_social/screen/chat/lib/presentation/view/communication_view.dart';
import 'package:toii_social/screen/chat/lib/presentation/view/connected_view.dart';
import 'package:toii_social/screen/chat/lib/presentation/view/discovery_view.dart';
import 'package:toii_social/screen/chat/lib/presentation/view/permissions_view.dart';
import 'package:toii_social/screen/chat/lib/presentation/view/ready_view.dart';
import 'package:toii_social/screen/chat/lib/presentation/view/select_client_type_view.dart';
import 'package:toii_social/screen/chat/lib/presentation/view/streaming_peers_view.dart';

class AppStepViewBuilder {
  const AppStepViewBuilder({required this.state});

  final AppState state;

  Widget buildContent({
    required GlobalKey<ScaffoldState> scaffoldKey,
  }) {
    return switch (state) {
      AppState.idle => const SizedBox(),
      (AppState.permissions) => const PermissionsView(),
      (AppState.checkServices) => const CheckServiceView(),
      (AppState.selectClientType) => const SelectClientTypeView(),
      (AppState.readyToDiscover) => const ReadyView(),
      (AppState.discoveringPeers) => const DiscoveryView(),
      (AppState.streamingPeers) => const StreamingPeersView(),
      (AppState.loadingConnection) => const Center(
          child: CircularProgressIndicator.adaptive(),
        ),
      (AppState.connected) => ConnectedView(
          scaffoldKey: scaffoldKey,
        ),
      (AppState.communicationChannelCreated) => const CommunicationView(),
    };
  }

  Widget buildTitle() {
    return Text(
      switch (state) {
        AppState.idle => "Initializing...",
        AppState.permissions => "Provide permissions",
        AppState.checkServices => "Check services",
        AppState.selectClientType =>
          'Do you want to find your friend from this device?',
        AppState.readyToDiscover => "Ready to discover!",
        AppState.discoveringPeers => "Discovering devices...",
        AppState.streamingPeers => "Peers stream got!",
        AppState.loadingConnection => "Loading your connection",
        AppState.connected => "Connected!",
        AppState.communicationChannelCreated => "You can communicate!",
      },
      style: const TextStyle(fontSize: 14),
    );
  }

  Widget? buildSubtitle() {
    final subtitle = switch (state) {
      AppState.selectClientType =>
        'Click "Yes" if you will search, click "No" if you will wait for your friend to connect',
      _ => null,
    };
    return subtitle != null ? Text(subtitle) : null;
  }
}
