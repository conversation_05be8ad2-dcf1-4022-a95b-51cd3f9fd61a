import 'package:flutter/material.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/widget/colors/colors.dart';

class DotPageIndicator extends AnimatedWidget {
  final TabController tabController;

  const DotPageIndicator({super.key, required this.tabController})
    : super(listenable: tabController);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 8, left: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: List.generate(
          tabController.length,
          (index) => AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            margin: const EdgeInsets.only(right: 8),
            height: 8,
            width: tabController.index == index ? 24 : 8,
            decoration: BoxDecoration(
              color:
                  tabController.index == index
                      ? themeData.primaryGreen600
                      : themeData.primaryGreen500.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
      ),
    );
  }
}
