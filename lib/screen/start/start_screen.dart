import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/generated/l10n.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/widget/button/button.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class StartScreen extends StatelessWidget {
  const StartScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color.fromRGBO(0, 7, 8, 0.9),
      body: Stack(
        children: [
          Assets.images.welcome.image(
            width: double.infinity,
            fit: BoxFit.fitWidth,
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              color: Colors.transparent,
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Align(child: SvgPicture.asset(Assets.icons.icToii.path)),
                  const SizedBox(height: 16),
                  Align(
                    child: Text(
                      textAlign: TextAlign.center,
                      S.current.start_welcome,
                      style: headlineSmall.copyColor(themeData.neutral50),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 40),
                    child: Text(
                      textAlign: TextAlign.center,
                      S.current.start_description,
                      style: bodyMedium.copyColor(themeData.neutral200),
                    ),
                  ),
                  const SizedBox(height: 58),
                  TSButton.secondary(
                    title: S.current.start_register,
                    onPressed: () {
                      context.push(RouterEnums.register.routeName);
                    },
                  ),
                  const SizedBox(height: 16),
                  TSButton.primary(
                    title: S.current.start_login,
                    onPressed: () {
                      context.push(RouterEnums.login.routeName);
                    },
                  ),
                  SizedBox(height: MediaQuery.of(context).padding.bottom + 16),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
