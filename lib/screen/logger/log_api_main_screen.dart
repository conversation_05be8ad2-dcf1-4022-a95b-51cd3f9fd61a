import 'package:debounce_throttle/debounce_throttle.dart';
import 'package:flutter/material.dart';
import 'package:toii_social/model/log_api/log_api_singleton.dart';
import 'package:toii_social/screen/logger/log_api_detail_screen.dart';

class LogAPIScreen extends StatefulWidget {
  const LogAPIScreen({Key? key}) : super(key: key);

  @override
  State<LogAPIScreen> createState() => _LogAPIScreenState();
}

class _LogAPIScreenState extends State<LogAPIScreen> {
  final textController = TextEditingController();

  LogAPISingleTon singleTon = LogAPISingleTon();

  late List<LogAPIModel> data = [...singleTon.data];

  late Debouncer<String> debouncer = Debouncer<String>(
    const Duration(milliseconds: 300),
    initialValue: '',
  );

  @override
  void initState() {
    super.initState();

    textController.addListener(() => debouncer.value = textController.text);

    debouncer.values.listen((text) => onSearch(text.trim().toLowerCase()));
  }

  @override
  void dispose() {
    debouncer.cancel();
    textController.dispose();
    super.dispose();
  }

  void clear() {
    data.clear();
    singleTon.data.clear();
    textController.clear();
    setState(() {});
  }

  Future onRefresh() async {
    onSearch(textController.text.trim().toLowerCase());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.clear, color: Colors.white),
        ),
        title: const Text(
          'Log API Screen',
          style: TextStyle(color: Colors.white),
        ),
        actions: [
          TextButton(
            onPressed: () => clear(),
            child: const Text(
              'Clear',
              style: TextStyle(color: Colors.white),
            ),
          )
        ],
        backgroundColor: Colors.green,
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(12),
            child: TextField(
              controller: textController,
              textInputAction: TextInputAction.done,
              decoration: InputDecoration(
                hintText: 'Tìm kiếm',
                hintStyle: const TextStyle(fontSize: 12, color: Colors.black45),
                suffixIcon: GestureDetector(
                  onTap: () => onClear(),
                  child: const Icon(Icons.close, color: Colors.black54),
                ),
              ),
            ),
          ),
          Expanded(
            child: RefreshIndicator(
              onRefresh: () => onRefresh(),
              child: ListView.separated(
                keyboardDismissBehavior:
                    ScrollViewKeyboardDismissBehavior.onDrag,
                padding: const EdgeInsets.symmetric(vertical: 12),
                itemBuilder: (_, index) => _ChildItem(model: data[index]),
                separatorBuilder: (_, __) => const SizedBox(height: 20),
                itemCount: data.length,
              ),
            ),
          )
        ],
      ),
    );
  }

  void onClear() {
    textController.clear();
    setState(() => data = [...singleTon.data]);
  }

  void onSearch(String text) {
    if (text.isEmpty) {
      setState(() => data = [...singleTon.data]);
      return;
    }

    final list = singleTon.data
        .where((e) => e.uri.toLowerCase().contains(text))
        .toList();

    setState(() => data = list);
  }
}

class _ChildItem extends StatelessWidget {
  const _ChildItem({Key? key, required this.model}) : super(key: key);

  final LogAPIModel model;

  void _handleNavigateToAPIDetail(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => LogAPIDetailScreen(logAPIModel: model),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: model.isError ? Colors.red : Colors.green,
      child: InkWell(
        onTap: () => _handleNavigateToAPIDetail(context),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(
            '${model.status} : ${model.uri}',
            style: const TextStyle(color: Colors.white),
          ),
        ),
      ),
    );
  }
}
