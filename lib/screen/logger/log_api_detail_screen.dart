import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:toii_social/model/log_api/log_api_singleton.dart';

class LogAPIDetailScreen extends StatefulWidget {
  const LogAPIDetailScreen({required this.logAPIModel, super.key});
  final LogAPIModel logAPIModel;

  @override
  State<LogAPIDetailScreen> createState() => _LogAPIDetailScreenState();
}

class _LogAPIDetailScreenState extends State<LogAPIDetailScreen>
    with SingleTickerProviderStateMixin {
  late final TabController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TabController(length: 4, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'API Detail',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.green,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Column(
        children: [
          TabBar(
            controller: _controller,
            labelColor: Colors.green,
            indicatorColor: Colors.green,
            tabs: const [
              Tab(
                text: 'Headers',
              ),
              Tab(
                text: 'Body',
              ),
              Tab(
                text: 'Response',
              ),
              Tab(
                text: 'Curl',
              ),
            ],
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: TabBarView(
                physics: const NeverScrollableScrollPhysics(),
                controller: _controller,
                children: [
                  _HeaderTabView(
                    uri: widget.logAPIModel.uri,
                    method: widget.logAPIModel.method,
                    headers: widget.logAPIModel.headers,
                  ),
                  NormalTabView(
                    content: widget.logAPIModel.body,
                  ),
                  NormalTabView(
                    extraData: 'Status: ${widget.logAPIModel.status}',
                    content: widget.logAPIModel.response,
                  ),
                  NormalTabView(
                    content: widget.logAPIModel.cURL,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _HeaderTabView extends StatelessWidget {
  const _HeaderTabView({
    Key? key,
    required this.uri,
    required this.method,
    required this.headers,
  }) : super(key: key);

  final String uri;

  final String method;

  final String headers;

  void _handleOnPressClipboard(BuildContext context) {
    Clipboard.setData(ClipboardData(text: uri)).then((_) {
      ScaffoldMessenger.of(context)
          .showSnackBar(const SnackBar(content: Text("copied")));
    });
  }

  @override
  Widget build(BuildContext context) {
    const titleStyle = TextStyle(fontWeight: FontWeight.w600);
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Align(
            alignment: Alignment.topRight,
            child: IconButton(
              onPressed: () => _handleOnPressClipboard(context),
              icon: const Icon(
                Icons.copy,
                size: 20,
                color: Colors.green,
              ),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            // direction: Axis.horizontal,
            children: [
              const Text('Uri:   ', style: titleStyle),
              Expanded(child: Text(uri)),
            ],
          ),
          Row(
            children: [
              const Text('Method:   ', style: titleStyle),
              Text(method),
            ],
          ),
          const Text('Headers:   ', style: titleStyle),
          Text(headers),
        ],
      ),
    );
  }
}

class NormalTabView extends StatelessWidget {
  const NormalTabView({
    Key? key,
    required this.content,
    this.extraData,
    this.allowCopy = true,
  }) : super(key: key);

  final String? extraData;

  final String content;

  final bool allowCopy;

  void _handleOnPressClipboard(BuildContext context) {
    Clipboard.setData(ClipboardData(text: content)).then((_) {
      ScaffoldMessenger.of(context)
          .showSnackBar(const SnackBar(content: Text("copied")));
    });
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Wrap(
        children: [
          Visibility(
            visible: allowCopy,
            child: Align(
              alignment: Alignment.bottomRight,
              child: IconButton(
                onPressed: () => _handleOnPressClipboard(context),
                icon: const Icon(
                  Icons.copy,
                  size: 20,
                  color: Colors.green,
                ),
              ),
            ),
          ),
          const SizedBox(height: 24),
          if (extraData != null) ...[
            Text(extraData!),
            const SizedBox(height: 15),
          ],
          Text('\n$content'),
        ],
      ),
    );
  }
}
