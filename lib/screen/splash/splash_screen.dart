import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/router/app_router.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();

       _startSplashScreen();
  }

  Future<void> _startSplashScreen() async {
    await Future.delayed(const Duration(seconds: 1));
    await _onHandleCheckUserLogin();
  }

  Future<void> _onHandleCheckUserLogin() async {
    // final sharedPrefs = GetIt.instance<SharedPreferencesManager>();
    // final token = sharedPrefs.getString(TOKEN_KEY) ?? "";
    // final isLogged = sharedPrefs.getBool(IS_LOGIN_KEY) ?? false;
    // final isVisitedOnBoarding = sharedPrefs.getBool(ONBOARDING_KEY) ?? false;

    // late String routerName;

    // if (isVisitedOnBoarding == false) {
    //   routerName = RouterEnums.onboarding.routeName;
    // } else if (token.isNotEmpty && isLogged == true) {
    //   routerName = RouterEnums.dashboard.routeName;
    // } else {
    //   routerName = RouterEnums.login.routeName;
    // }

    context.go(RouterEnums.start.routeName);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center, // Center the content
          children: [
            // Expanded(
            //   child: Center(
            //     child: SvgPicture.asset(
            //       logo,
            //       width: 171,
            //     ),
            //   ),
            // ),
            // Padding(
            //   padding: EdgeInsets.only(bottom: bottomPadding),
            //   child: Text(
            //     'Power On',
            //     style: AppTextStyle.body1Regular.copyWith(
            //       color: textColor,
            //     ),
            //   ),
            // ),
          ],
        ),
      ),
    );
  }

  showAlertDialog(String message) {
    // AlertDialog alert = AlertDialog(
    //   title: Text("Error"),
    //   content: Text(RunXLocalize.current.error),
    // );

    // show the dialog
    // showDialog(
    //   context: context,
    //   builder: (BuildContext context) {
    //     return alert;
    //   },
    // );
  }
}
