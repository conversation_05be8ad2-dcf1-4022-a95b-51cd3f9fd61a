import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_social/cubit/follower/follower_cubit.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

import 'user_list_item.dart';

class FollowerList extends StatelessWidget {
  const FollowerList({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FollowerCubit, FollowerState>(
      builder: (context, state) {
        if (state.status.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state.status.isFailure) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Failed to load followers',
                  style: titleMedium.copyWith(
                    color: context.themeData.neutral400,
                  ),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    context.read<FollowerCubit>().refreshFollowers();
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        if (state.followers.isEmpty) {
          return Center(
            child: Text(
              'No followers yet',
              style: titleMedium.copyWith(color: context.themeData.neutral400),
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.symmetric(vertical: 8),
          itemCount: state.followers.length,
          itemBuilder: (context, index) {
            final user = state.followers[index];
            return UserListItem(user: user);
          },
        );
      },
    );
  }
}
