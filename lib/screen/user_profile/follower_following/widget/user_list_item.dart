import 'package:flutter/material.dart';
import 'package:toii_social/model/user/user_model.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class UserListItem extends StatelessWidget {
  const UserListItem({super.key, required this.user, this.isFollowing = false});

  final UserModel user;
  final bool isFollowing;

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // Avatar
          CircleAvatar(
            radius: 20,
            backgroundImage:
                user.avatar != null && user.avatar!.isNotEmpty
                    ? NetworkImage(user.avatar!)
                    : null,
            child:
                user.avatar == null || user.avatar!.isEmpty
                    ? Text(
                      (user.fullName ?? user.username).isNotEmpty
                          ? (user.fullName ?? user.username)[0].toUpperCase()
                          : '',
                      style: titleMedium.copyWith(color: themeData.neutral50),
                    )
                    : null,
          ),
          const SizedBox(width: 12),
          // User Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.fullName ?? user.username,
                  style: titleMedium.copyWith(color: themeData.neutral800),
                ),
                if (user.username.isNotEmpty)
                  Text(
                    '@${user.username}',
                    style: bodyMedium.copyWith(color: themeData.neutral400),
                  ),
              ],
            ),
          ),
          // Follow Button
          ElevatedButton(
            onPressed: () {
              // TODO: Implement follow/unfollow functionality
            },
            style: ElevatedButton.styleFrom(
              backgroundColor:
                  isFollowing
                      ? themeData.neutral200
                      : themeData.primaryGreen500,
              foregroundColor:
                  isFollowing ? themeData.neutral800 : themeData.neutral50,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              minimumSize: const Size(0, 32),
            ),
            child: Text(
              isFollowing ? 'Following' : 'Follow back',
              style: bodyMedium.copyWith(
                color: isFollowing ? themeData.neutral800 : themeData.neutral50,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
