import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/cubit/follower/follower_cubit.dart';
import 'package:toii_social/model/user/user_model.dart';
import 'package:toii_social/widget/app_bar/app_bar.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

import 'widget/follower_list.dart';
import 'widget/following_list.dart';
import 'widget/tab_button.dart';

class FollowerFollowingScreen extends StatefulWidget {
  const FollowerFollowingScreen({super.key, this.user, this.initialTab = 0});

  final UserModel? user;
  final int initialTab;

  @override
  State<FollowerFollowingScreen> createState() =>
      _FollowerFollowingScreenState();
}

class _FollowerFollowingScreenState extends State<FollowerFollowingScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late FollowerCubit _followerCubit;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 2,
      vsync: this,
      initialIndex: widget.initialTab,
    );
    _followerCubit = GetIt.instance<FollowerCubit>();

    // Load initial data based on the selected tab
    if (widget.initialTab == 0) {
      _followerCubit.getFollowers();
    } else {
      _followerCubit.getFollowing();
    }

    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        if (_tabController.index == 0) {
          _followerCubit.getFollowers();
        } else {
          _followerCubit.getFollowing();
        }
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;

    return BlocProvider.value(
      value: _followerCubit,
      child: Scaffold(
        backgroundColor: themeData.neutral50,
        appBar: BaseAppBar(
          title: widget.user?.username ?? widget.user?.fullName ?? 'User',
          centerTitle: false,
          actions: [
            IconButton(
              icon: Icon(Icons.person_add, color: themeData.neutral400),
              onPressed: () {
                // TODO: Implement add friend functionality
              },
            ),
          ],
        ),
        body: Column(
          children: [
            // Tab Bar Section
            Container(
              color: themeData.neutral50,
              child: Column(
                children: [
                  // Tab Navigation
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: AnimatedBuilder(
                      animation: _tabController,
                      builder: (context, child) {
                        return Row(
                          children: [
                            Expanded(
                              child: TabButton(
                                title: 'Followers',
                                isActive: _tabController.index == 0,
                                onTap: () {
                                  _tabController.animateTo(0);
                                },
                              ),
                            ),
                            Expanded(
                              child: TabButton(
                                title: 'Following',
                                isActive: _tabController.index == 1,
                                onTap: () {
                                  _tabController.animateTo(1);
                                },
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                  // Follower Count
                  AnimatedBuilder(
                    animation: _tabController,
                    builder: (context, child) {
                      return BlocBuilder<FollowerCubit, FollowerState>(
                        builder: (context, state) {
                          final count =
                              _tabController.index == 0
                                  ? state.totalFollowers
                                  : state.totalFollowing;
                          final label =
                              _tabController.index == 0
                                  ? 'Followers'
                                  : 'Following';

                          return Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                            alignment: Alignment.centerLeft,
                            child: Text(
                              '$count $label',
                              style: titleMedium.copyWith(
                                color: themeData.neutral400,
                              ),
                            ),
                          );
                        },
                      );
                    },
                  ),
                ],
              ),
            ),
            // Content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: const [FollowerList(), FollowingList()],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
