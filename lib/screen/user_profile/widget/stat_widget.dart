import 'package:flutter/material.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class StatWidget extends StatelessWidget {
  final String label;
  final String count;
  final VoidCallback? onTap;

  const StatWidget({
    super.key,
    required this.label,
    required this.count,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;
    final child = Column(
      children: [
        Text(count, style: titleLarge.copyWith(color: themeData.neutral800)),
        const SizedBox(height: 4),
        Text(label, style: labelLarge.copyWith(color: themeData.neutral400)),
      ],
    );

    if (onTap != null) {
      return GestureDetector(onTap: onTap, child: child);
    }

    return child;
  }
}