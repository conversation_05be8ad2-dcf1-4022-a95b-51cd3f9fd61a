import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/user/user_model.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/widget/button/button.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/screen/user_profile/widget/stat_widget.dart';

class ProfileCard extends StatelessWidget {
  const ProfileCard({super.key, required this.user});
  final UserModel? user;

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;
    return Stack(
      clipBehavior: Clip.none,
      alignment: Alignment.topCenter,
      children: [
        Container(
          margin: const EdgeInsets.only(
            top: 48,
            left: 20,
            right: 20,
            bottom: 0,
          ),
          padding: const EdgeInsets.only(top: 60, bottom: 20),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.95),
            borderRadius: BorderRadius.circular(30),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.06),
                blurRadius: 16,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            children: [
              Text(
                user?.username ?? user?.fullName ?? '-',
                style: titleLarge.copyWith(color: themeData.neutral800),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24.0,
                  vertical: 8,
                ),
                child: Text.rich(
                  TextSpan(
                    text:
                        "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ",
                    style: labelLarge.copyWith(color: themeData.neutral400),
                    children: [
                      TextSpan(
                        text: "See more",
                        style: labelLarge.copyWith(
                          color: themeData.primaryGreen500,
                        ),
                      ),
                    ],
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  StatWidget(
                    label: "Follower",
                    count: "-",
                    onTap: () {
                      context.push(
                        RouterEnums.followerFollowing.routeName,
                        extra: user,
                      );
                    },
                  ),
                  StatWidget(
                    label: "Following",
                    count: "-",
                    onTap: () {
                      context.push(
                        RouterEnums.followerFollowing.routeName,
                        extra: user,
                      );
                    },
                  ),
                  const StatWidget(label: "Posts", count: "-"),
                ],
              ),
              const SizedBox(height: 20),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  children: [
                    Expanded(
                      child: TSButton.primary(
                        size: ButtonSize.small,
                        title: "Follow",
                        onPressed: () {},
                      ),
                    ),
                    const SizedBox(width: 12),
                    Assets.icons.icIbox.svg(),
                  ],
                ),
              ),
            ],
          ),
        ),
        Positioned(
          top: 0,
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.white, width: 4),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.08),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: CircleAvatar(
              radius: 48,
              backgroundImage: NetworkImage(
                user?.avatar ??
                    'https://randomuser.me/api/portraits/women/47.jpg',
              ),
            ),
          ),
        ),
      ],
    );
  }
}