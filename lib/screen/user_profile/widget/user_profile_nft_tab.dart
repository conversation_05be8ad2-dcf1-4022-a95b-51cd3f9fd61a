import 'package:flutter/material.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class UserProfileNftTab extends StatelessWidget {
  const UserProfileNftTab({super.key});

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;
    final nfts = List.generate(
      6,
      (i) => _NftItemData(
        image: Assets.images.nftSample.path,
        name: 'Hedgie #${2314 + i}',
        author: i % 2 == 0 ? 'Williamson' : 'Attican',
        avatar:
            'https://randomuser.me/api/portraits/${i % 2 == 0 ? 'men/32' : 'women/47'}.jpg',
        liked: i % 2 == 0,
        likes: 12 + i,
      ),
    );
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: nfts.length,
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          childAspectRatio: 171 / 250,
        ),
        itemBuilder: (context, i) => _NftCard(item: nfts[i]),
      ),
    );
  }
}

class _NftItemData {
  final String image;
  final String name;
  final String author;
  final String avatar;
  final bool liked;
  final int likes;
  _NftItemData({
    required this.image,
    required this.name,
    required this.author,
    required this.avatar,
    required this.liked,
    required this.likes,
  });
}

class _NftCard extends StatelessWidget {
  final _NftItemData item;
  const _NftCard({required this.item});

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;
    return Container(
      decoration: BoxDecoration(
        // color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            children: [
              ClipRRect(
                borderRadius: const BorderRadius.all(Radius.circular(16)),
                child: Image.asset(
                  item.image,
                  width: double.infinity,
                  height: 160,
                  fit: BoxFit.fitWidth,
                  errorBuilder:
                      (c, e, s) => Container(
                        width: double.infinity,
                        height: 160,
                        color: themeData.primaryGreen50,
                        child: const Icon(
                          Icons.image,
                          size: 40,
                          color: Colors.grey,
                        ),
                      ),
                ),
              ),
              Positioned(
                top: 10,
                right: 10,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.5),
                    shape: BoxShape.circle,
                  ),
                  padding: const EdgeInsets.all(4),
                  child: Icon(
                    item.liked ? Icons.favorite : Icons.favorite_border,
                    color:
                        item.liked ? themeData.primaryGreen500 : Colors.white,
                    size: 18,
                  ),
                ),
              ),
            ],
          ),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),

            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 14,
                  backgroundImage: NetworkImage(item.avatar),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.name,
                        style: titleLarge.copyWith(
                          fontSize: 14,
                          color: themeData.neutral800,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 2),
                      Text(
                        'by ${item.author}',
                        style: labelLarge.copyWith(
                          fontSize: 12,
                          color: themeData.neutral400,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
