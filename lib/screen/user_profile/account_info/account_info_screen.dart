import 'package:flutter/material.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/screen/user_profile/account_info/widget/account_info_list_item.dart';
import 'package:toii_social/widget/app_bar/app_bar.dart';
import 'package:toii_social/widget/colors/colors.dart';

class AccountInfoScreen extends StatelessWidget {
  const AccountInfoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;

    return Scaffold(
      backgroundColor: themeData.neutral50,
      appBar: const BaseAppBar(title: 'Account', centerTitle: false),
      body: Column(
        children: [
          // Account Information List
          Expanded(
            child: Column(
              children: [
                AccountInfoListItem(
                  icon: Assets.icons.icProfile.svg(
                    colorFilter: ColorFilter.mode(
                      themeData.textSecondary,
                      BlendMode.srcIn,
                    ),
                  ),
                  title: 'Username',
                  subtitle: 'Alice2432',
                  onTap: () {
                    // TODO: Navigate to username edit
                  },
                ),
                AccountInfoListItem(
                  icon: Assets.icons.icSms.svg(
                    colorFilter: ColorFilter.mode(
                      themeData.textSecondary,
                      BlendMode.srcIn,
                    ),
                  ),
                  title: 'Email',
                  subtitle: '<EMAIL>',
                  showArrow: false,
                  onTap: () {
                    // TODO: Navigate to email edit
                  },
                ),
                AccountInfoListItem(
                  icon: Assets.icons.icCalendar.svg(
                    colorFilter: ColorFilter.mode(
                      themeData.textSecondary,
                      BlendMode.srcIn,
                    ),
                  ),
                  title: 'Birthday',
                  subtitle: 'Update',
                  onTap: () {
                    // TODO: Navigate to birthday edit
                  },
                ),
                AccountInfoListItem(
                  icon: Assets.icons.icLock2Svg_.svg(
                    colorFilter: ColorFilter.mode(
                      themeData.textSecondary,
                      BlendMode.srcIn,
                    ),
                  ),
                  title: 'Change password',
                  onTap: () {
                    // TODO: Navigate to change password
                  },
                ),
                AccountInfoListItem(
                  icon: Assets.icons.icLocation.svg(
                    colorFilter: ColorFilter.mode(
                      themeData.textSecondary,
                      BlendMode.srcIn,
                    ),
                  ),
                  title: 'Location',
                  subtitle: 'Việt Nam',
                  onTap: () {
                    // TODO: Navigate to location edit
                  },
                ),
                AccountInfoListItem(
                  icon: Assets.icons.icCloseCircle.svg(
                    colorFilter: ColorFilter.mode(
                      themeData.red500,
                      BlendMode.srcIn,
                    ),
                  ),
                  title: 'Delete account',
                  titleColor: themeData.red500,
                  onTap: () {
                    // TODO: Handle delete account
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
