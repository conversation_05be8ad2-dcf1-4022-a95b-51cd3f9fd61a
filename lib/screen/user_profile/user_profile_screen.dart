import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/user/user_model.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/user_profile/widget/profile_card.dart';
import 'package:toii_social/screen/user_profile/widget/user_profile_post_list.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class UserProfileScreen extends StatelessWidget {
  const UserProfileScreen({super.key, this.user});
  final UserModel? user;

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;
    return Scaffold(
      backgroundColor: themeData.neutral100,
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: MediaQuery.of(context).size.height,
            pinned: true,
            stretch: true,
            backgroundColor: Colors.transparent,
            elevation: 0,
            leading: IconButton(
              icon: Icon(Icons.arrow_back, color: themeData.neutral50),
              onPressed: () => Navigator.pop(context),
            ),
            title: Text(
              user?.username ?? user?.fullName ?? '-',
              style: titleLarge.copyWith(color: themeData.neutral50),
            ),
            flexibleSpace: FlexibleSpaceBar(
              stretchModes: const [
                StretchMode.zoomBackground,
                StretchMode.fadeTitle,
              ],
              background: Stack(
                fit: StackFit.expand,
                children: [
                  Assets.images.defaultBackground.image(fit: BoxFit.cover),
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.black.withOpacity(0.25),
                          Colors.transparent,
                        ],
                      ),
                    ),
                  ),

                  SafeArea(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Align(
                          alignment: Alignment.bottomCenter,
                          child: Padding(
                            padding: const EdgeInsets.only(bottom: 0),
                            child: ProfileCard(user: user),
                          ),
                        ),
                        const SizedBox(height: 12),
                        SafeArea(
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              vertical: 16,
                              horizontal: 20,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(24),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.03),
                                  blurRadius: 8,
                                  offset: Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                  width: 40,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    color: Colors.grey[200],
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    Icons.keyboard_arrow_down,
                                    color: Colors.grey[700],
                                    size: 28,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Text(
                                  'Scroll down to see more',
                                  style: TextStyle(
                                    color: Colors.grey[700],
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(
                          height: MediaQuery.of(context).padding.bottom + 34,
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              titlePadding: const EdgeInsets.only(left: 56, bottom: 16),
            ),
            actions: [
              IconButton(
                icon: Icon(Icons.more_vert, color: themeData.neutral50),
                onPressed: () {},
              ),
              GestureDetector(
                onTap: () {
                  context.push(RouterEnums.settingProfile.routeName);
                },
                child: Assets.icons.icSetting.svg(),
              ),
            ],
          ),
          // Tabs + Post List
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.only(top: 24),
              child: UserProfilePostList(user: user),
            ),
          ),
        ],
      ),
    );
  }
}
