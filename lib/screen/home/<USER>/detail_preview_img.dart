import 'dart:async'; // Added for Completer
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:readmore/readmore.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/screen/home/<USER>/post/post_action_bar.dart';
import 'package:toii_social/utils/time_utils.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class DetailPreviewImgArg {
  final List<String> images;
  final int initialIndex;
  final PostModel? post;

  DetailPreviewImgArg({
    required this.images,
    required this.initialIndex,
    this.post,
  });
}

class DetailPreviewImg extends StatefulWidget {
  final DetailPreviewImgArg arg;

  const DetailPreviewImg({super.key, required this.arg});

  @override
  _DetailPreviewImgState createState() => _DetailPreviewImgState();
}

class _DetailPreviewImgState extends State<DetailPreviewImg>
    with TickerProviderStateMixin {
  late PageController _pageController;
  bool _showOverlay = true;
  final Map<String, Size> _imageSizes = {};
  late AnimationController _overlayAnimationController;
  late Animation<double> _overlayAnimation;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: widget.arg.initialIndex);
    _preloadImageSizes();

    _overlayAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _overlayAnimation = Tween<double>(begin: 1.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _overlayAnimationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _overlayAnimationController.dispose();
    super.dispose();
  }

  void _preloadImageSizes() {
    for (String imagePath in widget.arg.images) {
      _getImageSize(imagePath);
    }
  }

  Future<void> _getImageSize(String imagePath) async {
    try {
      final ImageProvider provider = _getImageProvider(imagePath);
      final ImageStream stream = provider.resolve(ImageConfiguration.empty);
      final Completer<Size> completer = Completer<Size>();

      stream.addListener(
        ImageStreamListener((ImageInfo info, bool _) {
          completer.complete(
            Size(info.image.width.toDouble(), info.image.height.toDouble()),
          );
        }),
      );

      final Size size = await completer.future;
      setState(() {
        _imageSizes[imagePath] = size;
      });
    } catch (e) {
      // Fallback to default size if image loading fails
      setState(() {
        _imageSizes[imagePath] = const Size(1080, 1920); // Default 9:16
      });
    }
  }

  double _calculateAspectRatio(String imagePath) {
    final Size? size = _imageSizes[imagePath];
    if (size == null) return 16 / 9; // Default aspect ratio

    return size.width / size.height;
  }

  void _toggleOverlay() {
    setState(() {
      _showOverlay = !_showOverlay;
    });

    if (_showOverlay) {
      _overlayAnimationController.forward();
    } else {
      _overlayAnimationController.reverse();
    }
  }

  ImageProvider _getImageProvider(String path) {
    if (path.startsWith('http://') || path.startsWith('https://')) {
      return NetworkImage(path);
    } else {
      return FileImage(File(path));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Fullscreen image gallery
          PhotoViewGallery.builder(
            itemCount: widget.arg.images.length,
            pageController: _pageController,
            onPageChanged: (index) {
              setState(() {});
            },
            builder: (context, index) {
              final String imagePath = widget.arg.images[index];
              return PhotoViewGalleryPageOptions(
                imageProvider: _getImageProvider(imagePath),
                heroAttributes: PhotoViewHeroAttributes(tag: imagePath),
                initialScale: PhotoViewComputedScale.contained,
                minScale: PhotoViewComputedScale.contained * 0.5,
                maxScale: PhotoViewComputedScale.contained * 4,
                basePosition: Alignment.center,
                tightMode: true,
                errorBuilder:
                    (context, error, stackTrace) => Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: Colors.white54,
                            size: 48,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'Failed to load image',
                            style: bodyMedium.copyWith(color: Colors.white54),
                          ),
                        ],
                      ),
                    ),
              );
            },
            loadingBuilder:
                (context, event) => Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(
                        color: themeData.primaryGreen500,
                        strokeWidth: 2,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'Loading...',
                        style: bodyMedium.copyWith(color: Colors.white70),
                      ),
                    ],
                  ),
                ),
          ),

          // Top navigation bar
          AnimatedOpacity(
            opacity: _showOverlay ? 1.0 : 0.0,
            duration: Duration(milliseconds: 300),
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Colors.black.withOpacity(0.6), Colors.transparent],
                ),
              ),
              child: SafeArea(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Row(
                    children: [
                      GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: Container(
                          padding: EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.5),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Icon(
                            Icons.arrow_back_ios_new,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ),
                      Spacer(),
                      if (widget.arg.images.length > 1)
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.5),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '${(_pageController.hasClients ? _pageController.page?.round() ?? widget.arg.initialIndex : widget.arg.initialIndex) + 1}/${widget.arg.images.length}',
                            style: bodySmall.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Bottom overlay with post info
          if (widget.arg.post != null)
            AnimatedPositioned(
              duration: Duration(milliseconds: 300),
              bottom: _showOverlay ? 0 : -200,
              left: 0,
              right: 0,
              child: IgnorePointer(
                ignoring: false,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withOpacity(0.5),
                        Colors.black.withOpacity(0.9),
                      ],
                    ),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (widget.arg.post?.content.isNotEmpty ?? false)
                        Padding(
                          padding: EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  CircleAvatar(
                                    radius: 20,
                                    backgroundImage: NetworkImage(
                                      widget.arg.post?.user?.avatar ??
                                          "https://i.pravatar.cc/150?img=3",
                                    ),
                                  ),
                                  SizedBox(width: 12),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          widget.arg.post?.user?.username ??
                                              widget.arg.post?.user?.fullName ??
                                              '',
                                          style: titleMedium.copyWith(
                                            color: Colors.white,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                        Text(
                                          timeAgoSinceDate(
                                            widget.arg.post?.updatedAt ?? '',
                                          ),
                                          style: labelMedium.copyWith(
                                            color: Colors.white70,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              if (widget.arg.post?.content.isNotEmpty ?? false)
                                Padding(
                                  padding: EdgeInsets.symmetric(vertical: 8),
                                  child: ReadMoreText(
                                    widget.arg.post?.content ?? '',
                                    trimLines: 2,
                                    trimMode: TrimMode.Line,
                                    trimCollapsedText: ' See more',
                                    trimExpandedText: ' Show less',
                                    style: bodyLarge.copyWith(
                                      color: Colors.white,
                                      height: 1.4,
                                    ),
                                    moreStyle: bodyMedium.copyWith(
                                      color: themeData.primaryGreen500,
                                      fontWeight: FontWeight.w600,
                                    ),
                                    lessStyle: bodyMedium.copyWith(
                                      color: themeData.primaryGreen500,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      PostActionBar(
                        post:
                            widget.arg.post ??
                            PostModel(
                              id: '',
                              content: '',
                              reposts: 0,
                              comments: 0,
                            ),
                      ),
                      SizedBox(
                        height: MediaQuery.of(context).padding.bottom + 16,
                      ),
                    ],
                  ),
                ),
              ),
            ),

          // Tap to toggle overlay - but exclude interactive areas
          Positioned(
            top: 100, // Start below the top navigation area
            left: 0,
            right: 0,
            bottom: 0,
            child: IgnorePointer(
              ignoring:
                  widget.arg.post != null, // Ignore when post info is visible
              child: GestureDetector(
                onTap: _toggleOverlay,
                behavior: HitTestBehavior.opaque,
                child: Container(color: Colors.transparent),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
