// Privacy option for post
import 'package:flutter/material.dart';
import 'package:toii_social/gen/assets.gen.dart';

enum PrivacyOption { everyone, onlyMe, followersOnly }

extension PrivacyOptionExt on PrivacyOption {
  String get label {
    switch (this) {
      case PrivacyOption.everyone:
        return 'Everyone';
      case PrivacyOption.onlyMe:
        return 'Only me';
      case PrivacyOption.followersOnly:
        return 'Followers Only';
    }
  }

  String get apiValue {
    switch (this) {
      case PrivacyOption.everyone:
        return 'public';
      case PrivacyOption.onlyMe:
        return 'private';
      case PrivacyOption.followersOnly:
        return 'friends';
    }
  }

  // Optional: get icon for UI
  Widget get icon {
    switch (this) {
      case PrivacyOption.everyone:
        return Assets.icons.icGlobal.svg();
      case PrivacyOption.onlyMe:
        return Assets.icons.icLock.svg();
      case PrivacyOption.followersOnly:
        return Assets.icons.icProfile2user.svg();
    }
  }

  static PrivacyOption fromApiValue(String? value) {
    switch (value) {
      case 'public':
        return PrivacyOption.everyone;
      case 'private':
        return PrivacyOption.onlyMe;
      case 'friends':
        return PrivacyOption.followersOnly;
      default:
        return PrivacyOption.everyone;
    }
  }
}
