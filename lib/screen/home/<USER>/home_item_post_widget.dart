import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:readmore/readmore.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/home/<USER>/home_details_screen.dart';
import 'package:toii_social/screen/home/<USER>/post/post_action_bar.dart';
import 'package:toii_social/screen/home/<USER>/post/post_header.dart';
import 'package:toii_social/screen/home/<USER>/post/post_image_gallery.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class HomeItemPostWidget extends StatelessWidget {
  final PostModel post;
  final bool isNotOnTap;
  final bool isShowActionMore;
  const HomeItemPostWidget({
    super.key,
    required this.post,
    required this.isNotOnTap,
    required this.isShowActionMore,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: themeData.neutral200,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  PostHeader(post: post, isShowActionMore: isShowActionMore),
                  if (post.content.isNotEmpty)
                    GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () {
                        isNotOnTap
                            ? null
                            : context.push(
                              RouterEnums.homeDetails.routeName,
                              extra: HomeDetailsArguments(postModel: post),
                            );
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 4,
                        ),
                        child: ReadMoreText(
                          post.content,
                          trimLines: 2,
                          trimMode: TrimMode.Line,
                          trimCollapsedText: ' See more',
                          trimExpandedText: ' Show less',
                          style: bodyLarge.copyWith(
                            color: themeData.neutral800,
                          ),
                          moreStyle: labelLarge.copyColor(
                            themeData.primaryGreen500,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
              PostImageGallery(post: post),
              if (post.mediaUrls.isEmpty) const SizedBox(height: 32),
            ],
          ),
        ),
        PostActionBar(post: post),
      ],
    );
  }
}
