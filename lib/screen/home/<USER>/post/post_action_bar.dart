import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/home/<USER>/home_details_screen.dart';
import 'package:toii_social/screen/home/<USER>/post/post_icon_text.dart';
import 'package:toii_social/screen/home/<USER>/post/post_like_button.dart';
import 'package:toii_social/screen/home/<USER>/post/post_repost_button.dart';
import 'package:toii_social/screen/home/<USER>/post/post_share_button.dart';

class PostActionBar extends StatelessWidget {
  final PostModel post;
  const PostActionBar({super.key, required this.post});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 2),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.8),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            PostLikeButton(post: post, initialLiked: post.isLiked),
            const SizedBox(width: 16),
            PostIconText(
              icon: Assets.icons.icResponses.svg(width: 20, height: 20),
              text: post.getViewComment,
              onTap: () {
                context.push(
                  RouterEnums.homeDetails.routeName,
                  extra: HomeDetailsArguments(
                    postModel: post,
                    isForcusComment: true,
                  ),
                );
              },
            ),
            const SizedBox(width: 16),
            PostRepostButton( post: post),
            const SizedBox(width: 16),
            PostShareButton(post: post),
          ],
        ),
      ),
    );
  }
}
