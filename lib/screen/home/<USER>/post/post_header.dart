import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/cubit/post/delete_update_post/delete_update_cubit.dart';
import 'package:toii_social/cubit/post/delete_update_post/delete_update_state.dart';
import 'package:toii_social/cubit/post/hide_post/hide_post_cubit.dart';
import 'package:toii_social/cubit/post/hide_post/hide_post_state.dart';
import 'package:toii_social/cubit/post/home/<USER>';
import 'package:toii_social/cubit/report/report_cubit.dart';
import 'package:toii_social/cubit/report/report_state.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/home/<USER>/bottom_sheet_more_action.dart';
import 'package:toii_social/screen/home/<USER>/post/dialog_confirm_delete_post.dart';
import 'package:toii_social/utils/time_utils.dart';
import 'package:toii_social/widget/bottom_sheet/tt_bottom_sheet.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/images/avatar_widget.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';

class PostHeader extends StatelessWidget {
  final PostModel post;
  final bool isShowActionMore;
  const PostHeader({
    super.key,
    required this.post,
    required this.isShowActionMore,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.themeData;
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => DeleteUpdatePostCubit()),
        BlocProvider(create: (context) => HidePostCubit()),
        BlocProvider(create: (context) => ReportCubit()),
      ],
      child: Padding(
        padding: const EdgeInsets.fromLTRB(12, 12, 12, 0),
        child: Row(
          children: [
            GestureDetector(
              onTap: () {
                context.push(
                  RouterEnums.userProfile.routeName,
                  extra: post.user,
                );
              },
              child: AvatarWidget(
                size: 40,
                imageUrl: post.user?.avatar,
                name: post.user?.fullName ?? '',
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Flexible(
                        child: Text(
                          post.user?.username ?? post.user?.fullName ?? '',
                          style: titleMedium.copyWith(color: theme.neutral800),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: 6),
                      Container(
                        width: 3,
                        height: 3,
                        decoration: BoxDecoration(
                          color: theme.neutral300,
                          borderRadius: BorderRadius.circular(30),
                        ),
                      ),
                      const SizedBox(width: 6),
                      Text(
                        'Flow',
                        style: titleMedium.copyWith(
                          color: theme.primaryGreen500,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    timeAgoSinceDate(post.updatedAt ?? ''),
                    style: labelMedium.copyWith(color: theme.neutral300),
                  ),
                ],
              ),
            ),
            if (isShowActionMore) _moreAction(context, theme),
          ],
        ),
      ),
    );
  }

  Widget _moreAction(BuildContext context, ThemeData theme) {
    return MultiBlocListener(
      listeners: [
        BlocListener<DeleteUpdatePostCubit, DeleteUpdatePostState>(
          listener: (context, state) {
            if (state.status == DeleteUpdatePostStatus.success) {
              context.showSnackbar(message: "Post deleted successfully");
              GetIt.instance<HomeCubit>().getUserFeed();
            }
            if (state.status == DeleteUpdatePostStatus.failure) {
              context.showSnackbar(message: state.errorMessage ?? "");
            }
          },
        ),
        BlocListener<HidePostCubit, HidePostState>(
          listener: (context, state) {
            if (state.status == HidePostStatus.success) {
              context.showSnackbar(message: "Post hidden successfully");
              GetIt.instance<HomeCubit>().getUserFeed();
            }
            if (state.status == HidePostStatus.failure) {
              context.showSnackbar(message: state.errorMessage ?? "");
            }
          },
        ),
        BlocListener<ReportCubit, ReportState>(
          listener: (context, state) {
          
            if (state.status == ReportStatus.success) {
              context.showSnackbar(message: "Report submitted successfully");
            }
            if (state.status == ReportStatus.failure) {
              context.showSnackbar(message: state.errorMessage ?? "");
            } 
          },
        ),
      ],
      child: Builder(
        builder: (contextBuider) {
          return GestureDetector(
            onTap: () {
              showTtBottomSheet(
                context,
                child: BottomSheetMoreAction(
                  isPostByUser:
                      post.user?.id ==
                      GetIt.instance<ProfileCubit>().state.userModel?.id,
                  onEdit: () {
                    contextBuider
                        .push(RouterEnums.createPost.routeName, extra: post)
                        .then((value) {
                          GetIt.instance<HomeCubit>().getUserFeed();
                        });
                  },
                  onHide: () {
                    contextBuider.read<HidePostCubit>().hidePost(post.id);
                  },
                  onDelete: () {
                    showDialogConfirmDeletePost(
                      context: context,
                      onCancel: () {
                        context.pop();
                      },
                      onDelete: () {
                        context.pop();
                        contextBuider.read<DeleteUpdatePostCubit>().deletePost(
                          post.id,
                        );
                      },
                    );
                  },
                  onReport: (reasons, customReason) {
                    contextBuider.read<ReportCubit>().reportPost(
                      postId: post.id,
                      reasons: reasons,
                      customReason: customReason,
                    );
                  },
                ),

                isShowClose: true,
                isDismissible: true,
              );
            },
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16.0, 0, 0, 0),
              child: SvgPicture.asset(
                Assets.icons.icMore.path,
                colorFilter: ColorFilter.mode(
                  theme.neutral500,
                  BlendMode.srcIn,
                ),
                width: 24,
                height: 24,
              ),
            ),
          );
        },
      ),
    );
  }
}
