import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/home/<USER>/detail_preview_img.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/images/cached_network_image_widget.dart';

class PostImageGallery extends StatelessWidget {
  final PostModel post;
  const PostImageGallery({super.key, required this.post});

  @override
  Widget build(BuildContext context) {
    final theme = context.themeData;
    final images = post.mediaUrls;
    if (images.isEmpty) {
      return const SizedBox(height: 16);
    }
    int showCount = images.length > 4 ? 4 : images.length;

    if (showCount == 1) {
      return GestureDetector(
        onTap: () {
          context.push(
            RouterEnums.detailPreviewImg.routeName,
            extra: DetailPreviewImgArg(
              images: images,
              initialIndex: 0,
              post: post,
            ),
          );
        },
        child: Hero(
          tag: '${post.id}_${images[0]}',
          child: AspectRatio(
            aspectRatio: 1,
            child: CachedNetworkImageWidget(
              imageUrl: images[0],
              fit: BoxFit.cover,
              width: double.infinity,
              height: double.infinity,
              borderRadius: BorderRadius.circular(16),
            ),
          ),
        ),
      );
    } else if (showCount == 2) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        child: Row(
          children: [
            for (int i = 0; i < 2; i++)
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    context.push(
                      RouterEnums.detailPreviewImg.routeName,
                      extra: DetailPreviewImgArg(
                        images: images,
                        initialIndex: i,
                        post: post,
                      ),
                    );
                  },
                  child: Hero(
                    tag: '${post.id}_${images[i]}',
                    child: Padding(
                      padding: EdgeInsets.only(left: i == 1 ? 4 : 0),
                      child: AspectRatio(
                        aspectRatio: 1,
                        child: CachedNetworkImageWidget(
                          imageUrl: images[i],
                          fit: BoxFit.cover,
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      );
    } else if (showCount == 3) {
      return Column(
        children: [
          Row(
            children: [
              for (int i = 0; i < 2; i++)
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      context.push(
                        RouterEnums.detailPreviewImg.routeName,
                        extra: DetailPreviewImgArg(
                          images: images,
                          initialIndex: i,
                          post: post,
                        ),
                      );
                    },
                    child: Hero(
                      tag: '${post.id}_${images[i]}',
                      child: Padding(
                        padding: EdgeInsets.only(left: i == 1 ? 4 : 0),
                        child: AspectRatio(
                          aspectRatio: 1,
                          child: CachedNetworkImageWidget(
                            imageUrl: images[i],
                            fit: BoxFit.cover,
                            borderRadius: BorderRadius.circular(16),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 4),
          GestureDetector(
            onTap: () {
              context.push(
                RouterEnums.detailPreviewImg.routeName,
                extra: DetailPreviewImgArg(
                  images: images,
                  initialIndex: 2,
                  post: post,
                ),
              );
            },
            child: Hero(
              tag: '${post.id}_${images[2]}',
              child: AspectRatio(
                aspectRatio: 2,
                child: CachedNetworkImageWidget(
                  imageUrl: images[2],
                  fit: BoxFit.cover,
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ),
          ),
        ],
      );
    } else {
      final width = MediaQuery.of(context).size.width;
      final List<double> customHeights = [
        0.6 * width,
        0.5 * width,
        0.6 * width,
        0.5 * width,
      ];
      return MasonryGridView.builder(
        padding: EdgeInsets.zero,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverSimpleGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
        ),
        mainAxisSpacing: 4,
        crossAxisSpacing: 4,
        itemCount: showCount,
        itemBuilder: (context, index) {
          if (index == 2 && images.length > 4) {
            return Stack(
              children: [
                GestureDetector(
                  onTap: () {
                    context.push(
                      RouterEnums.detailPreviewImg.routeName,
                      extra: DetailPreviewImgArg(
                        images: images,
                        initialIndex: index,
                        post: post,
                      ),
                    );
                  },
                  child: Hero(
                    tag: '${post.id}_${images[index]}',
                    child: _buildImage(images[index], customHeights[index]),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    context.push(
                      RouterEnums.detailPreviewImg.routeName,
                      extra: DetailPreviewImgArg(
                        images: images,
                        initialIndex: index,
                        post: post,
                      ),
                    );
                  },
                  child: Container(
                    height: customHeights[index],
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Center(
                      child: Text(
                        "+${images.length - 4}",
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 26,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            );
          }
          return GestureDetector(
            onTap: () {
              context.push(
                RouterEnums.detailPreviewImg.routeName,
                extra: DetailPreviewImgArg(
                  images: images,
                  initialIndex: index,
                  post: post,
                ),
              );
            },
            child: Hero(
              tag: '${post.id}_${images[index]}',
              child: _buildImage(images[index], customHeights[index]),
            ),
          );
        },
      );
    }
  }

  Widget _buildImage(String url, double height) {
    return SizedBox(
      width: double.infinity,
      height: height,
      child: CachedNetworkImageWidget(
        imageUrl: url,
        fit: BoxFit.cover,
        borderRadius: BorderRadius.circular(16),
      ),
    );
  }
}
