import 'package:flutter/material.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/widget/bottom_sheet/tt_bottom_sheet.dart';
import 'package:toii_social/widget/button/button.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/text_field.dart/text_field.dart';
//spam_shilling, scam_phishing, hate_toxic, fake_impersonation, pump_dump, off_topic, other

enum ReportReason {
  spam('Spam/Shilling', 'spam_shilling'),
  scam('Scam/Phishing', 'scam_phishing'),
  hate('Hate/Toxic behavior', 'hate_toxic'),
  fake('Fake project/Impersonation', 'fake_impersonation'),
  pump('Pump & Dump manipulation', 'pump_dump'),
  offTopic('Off-topic/Low quality', 'off_topic'),
  other('Other', 'other');

  const ReportReason(this.displayName, this.apiValue);
  final String displayName;
  final String apiValue;
}

class BottomSheetReport extends StatefulWidget {
  const BottomSheetReport({
    super.key,
    this.onReport,
    this.commentId,
    this.postId,
    this.isComment = false,
  });

  final Function(List<ReportReason> reasons, String? customReason)? onReport;
  final String? commentId;
  final String? postId;
  final bool isComment;

  @override
  State<BottomSheetReport> createState() => _BottomSheetReportState();
}

class _BottomSheetReportState extends State<BottomSheetReport> {
  final Set<ReportReason> _selectedReasons = {};
  final TextEditingController _customReasonController = TextEditingController();

  @override
  void dispose() {
    _customReasonController.dispose();
    super.dispose();
  }

  void _toggleReason(ReportReason reason) {
    setState(() {
      if (_selectedReasons.contains(reason)) {
        _selectedReasons.remove(reason);
      } else {
        _selectedReasons.add(reason);
      }
    });
  }

  void _handleReport() {
    if (_selectedReasons.isEmpty) return;

    final customReason =
        _customReasonController.text.trim().isNotEmpty
            ? _customReasonController.text.trim()
            : null;

    widget.onReport?.call(_selectedReasons.toList(), customReason);
    Navigator.pop(context);
  }

  void _showCustomReasonBottomSheet() {
    showCustomReasonBottomSheet(
      context: context,
      initialValue: _customReasonController.text,
      onSave: (String reason) {
        setState(() {
          _customReasonController.text = reason;
        });
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.isComment ? 'Report comment' : 'Report post',
                style: headlineSmall.copyWith(color: themeData.neutral800),
              ),
              const SizedBox(height: 4),
              Text(
                widget.isComment
                    ? 'Why do you want to report this comment?'
                    : 'Why do you want to report this post?',
                style: labelMedium.copyWith(color: themeData.neutral400),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Report reasons list
          Flexible(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  ...ReportReason.values.map(
                    (reason) => _buildReasonItem(reason),
                  ),

                  // Custom reason text field
                  const SizedBox(height: 16),
                  GestureDetector(
                    onTap: _showCustomReasonBottomSheet,
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: themeData.neutral100,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              _customReasonController.text.isNotEmpty
                                  ? _customReasonController.text
                                  : 'Please specify...',
                              style: bodyMedium.copyWith(
                                color:
                                    _customReasonController.text.isNotEmpty
                                        ? themeData.neutral800
                                        : themeData.neutral500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: TSButton(
                  title: "Cancel",
                  size: ButtonSize.block,
                  elevatedVariant: ButtonVariant.black,
                  isEnabled: true,
                  onPressed: () => Navigator.pop(context),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: TSButton.primary(
                  isEnabled: true,
                  size: ButtonSize.block,
                  title: 'Report',
                  onPressed: _handleReport,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildReasonItem(ReportReason reason) {
    final isSelected = _selectedReasons.contains(reason);

    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: GestureDetector(
        onTap: () => _toggleReason(reason),
        child: Container(
          decoration: BoxDecoration(
            color: themeData.neutral50,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              // Checkbox
              SizedBox(
                width: 22,
                height: 22,
                child:
                    isSelected
                        ? Assets.icons.icChecked.svg(width: 22, height: 22)
                        : Assets.icons.icCheckbox.svg(
                          width: 22,
                          height: 22,
                          colorFilter: ColorFilter.mode(
                            themeData.neutral300,
                            BlendMode.srcIn,
                          ),
                        ),
              ),

              const SizedBox(width: 16),

              // Reason text
              Expanded(
                child: Text(
                  reason.displayName,
                  style: titleMedium.copyWith(color: themeData.neutral800),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CustomReasonBottomSheet extends StatefulWidget {
  const CustomReasonBottomSheet({
    super.key,
    this.initialValue = '',
    this.onSave,
  });

  final String initialValue;
  final Function(String reason)? onSave;

  @override
  State<CustomReasonBottomSheet> createState() =>
      _CustomReasonBottomSheetState();
}

class _CustomReasonBottomSheetState extends State<CustomReasonBottomSheet> {
  late final TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleSave() {
    final reason = _controller.text.trim();
    widget.onSave?.call(reason);
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    final bottomInset = MediaQuery.of(context).viewInsets.bottom;

    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Padding(
        padding: EdgeInsets.only(bottom: bottomInset),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                padding: const EdgeInsets.only(left: 16),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        'Report reason',
                        style: titleMedium.copyWith(
                          color: themeData.neutral500,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text(
                        'Cancel',
                        style: titleMedium.copyWith(
                          color: themeData.primaryGreen500,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    TextButton(
                      onPressed: _handleSave,
                      child: Text(
                        'Ok',
                        style: titleMedium.copyWith(
                          color: themeData.primaryGreen500,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Divider
              Container(height: 1, color: themeData.neutral100),

              // Content
              TTextField(
                showBorder: false,
                textController: _controller,
                hintText: 'Please specify',
                maxLines: 3,
                onEditingComplete: _handleSave,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Helper function to show the custom reason bottom sheet
Future<void> showCustomReasonBottomSheet({
  required BuildContext context,
  String initialValue = '',
  Function(String reason)? onSave,
}) {
  return showTtBottomSheet(
    context,
    child: CustomReasonBottomSheet(initialValue: initialValue, onSave: onSave),
  );
}

/// Helper function to show the report bottom sheet
Future<void> showReportBottomSheet({
  required BuildContext context,
  String? commentId,
  String? postId,
  final isComment = false,
  Function(List<ReportReason> reasons, String? customReason)? onReport,
}) {
  return showTtBottomSheet(
    context,
    child: BottomSheetReport(
      commentId: commentId,
      postId: postId,
      onReport: onReport,
    ),
  );
}
