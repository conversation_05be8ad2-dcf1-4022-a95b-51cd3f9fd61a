// import 'package:flutter/material.dart';
// import 'package:toii_social/cubit/theme/theme_cubit.dart';
// import 'package:toii_social/model/post/post_model.dart';
// import 'package:toii_social/screen/home/<USER>/post/post_header.dart';
// import 'package:toii_social/screen/home/<USER>/post/post_image_gallery.dart';
// import 'package:toii_social/screen/home/<USER>/post/post_like_button.dart';
// import 'package:toii_social/widget/colors/colors.dart';

// class HomeItemCommentWidget extends StatelessWidget {
//   final PostModel post;

//   const HomeItemCommentWidget({super.key, required this.post});

//   Widget _headerWidget() {
//     return Padding(
//       padding: const EdgeInsets.all(12.0),
//       child: PostHeader(post: post),
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       decoration: BoxDecoration(borderRadius: BorderRadius.circular(16)),
//       margin: const EdgeInsets.symmetric(horizontal: 16).copyWith(bottom: 16),
//       child: Stack(
//         children: [
//           Container(
//             height: 200,
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.circular(16),
//               color: themeData.black50,
//             ),
//           ),
//           Column(
//             children: [
//               _headerWidget(),
//               PostImageGallery(post: post),
//               child,
//               Container(height: 16),
//             ],
//           ),
//           Positioned(
//             bottom: 0,
//             child: PostLikeButton(post: post, initialLiked: post.isLiked),
//           ),
//         ],
//       ),
//     );
//   }
// }
