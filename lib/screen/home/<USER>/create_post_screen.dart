import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_social/cubit/post/create_post/create_post_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/screen/home/<USER>/enum/privacy_option_enum.dart';
import 'package:toii_social/screen/home/<USER>/widget/create_post_action_bar.dart';
import 'package:toii_social/screen/home/<USER>/widget/create_post_app_bar.dart';
import 'package:toii_social/screen/home/<USER>/widget/create_post_input.dart';
import 'package:toii_social/screen/home/<USER>/widget/image_grid.dart';
import 'package:toii_social/screen/home/<USER>/widget/privacy_content.dart';
import 'package:toii_social/utils/widget/dismiss_keyboard_widget.dart';
import 'package:toii_social/widget/bottom_sheet/tt_bottom_sheet.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';

class CreatePostScreen extends StatefulWidget {
  final PostModel? post;
  const CreatePostScreen({super.key, this.post});

  @override
  State<CreatePostScreen> createState() => _CreatePostScreenState();
}

class _CreatePostScreenState extends State<CreatePostScreen> {
  final TextEditingController _postController = TextEditingController();
  bool _initialized = false;

  @override
  void initState() {
    super.initState();
    _postController.addListener(_onPostChanged);
  }

  void _onPostChanged() {
    setState(() {});
  }

  @override
  void dispose() {
    _postController.removeListener(_onPostChanged);
    _postController.dispose();
    super.dispose();
  }

  Widget _body(BuildContext contextCubit) {
    return Stack(
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 16,
                  ),

                  child: Column(
                    children: [
                      CreatePostInput(controller: _postController),
                      const SizedBox(height: 32),
                      // Display selected images
                      BlocBuilder<CreatePostCubit, CreatePostState>(
                        builder: (context, state) {
                          return ImageGrid(
                            imagePaths: state.selectedImagePaths,
                            imageUrls: state.existingMediaUrls,
                            onRemove:
                                (index, isLocal) => context
                                    .read<CreatePostCubit>()
                                    .removeImage(index, isLocal),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),

            _actionBar(contextCubit),
          ],
        ),
        Positioned(bottom: 60, left: 16, child: Assets.icons.icGenAi.svg()),
      ],
    );
  }

  Widget _actionBar(BuildContext contextBloc) {
    return BlocBuilder<CreatePostCubit, CreatePostState>(
      builder: (context, state) {
        return CreatePostActionBar(
          onImage: () {
            try {
              _showImagePickerOptions(contextBloc);
            } catch (e) {
              print('Error showing image picker options: $e');
              contextBloc.showSnackbar(message: 'Error: $e');
            }
          },
          onPrivacy: () => showPrivacyBottomSheet(contextBloc),
        );
      },
    );
  }

  void _showImagePickerOptions(BuildContext parentContext) {
    final cubit = parentContext.read<CreatePostCubit>();
    showModalBottomSheet(
      context: parentContext,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext bottomSheetContext) {
        return SafeArea(
          child: Wrap(
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Select Image',
                  style: Theme.of(parentContext).textTheme.titleLarge,
                  textAlign: TextAlign.center,
                ),
              ),

              const SizedBox(height: 8),
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Choose from Gallery'),
                onTap: () async {
                  Navigator.pop(bottomSheetContext);
                  try {
                    await cubit.pickImages();
                  } catch (e) {
                    if (mounted) {
                      parentContext.showSnackbar(
                        message: 'Error selecting images: $e',
                      );
                    }
                  }
                },
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text('Take Photo'),
                onTap: () async {
                  Navigator.pop(bottomSheetContext);
                  try {
                    await cubit.pickImageFromCamera();
                  } catch (e) {
                    if (mounted) {
                      parentContext.showSnackbar(
                        message: 'Error taking photo: $e',
                      );
                    }
                  }
                },
              ),
              const SizedBox(height: 16),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return DismissKeyboardWidget(
      child: BlocProvider(
        create: (_) => CreatePostCubit()..initUpdatePost(widget.post),
        child: BlocConsumer<CreatePostCubit, CreatePostState>(
          listener: (context, state) {
            if (state.status.isSuccess) {
              context.showSnackbar(message: "Post created successfully");
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  Navigator.pop(context, state.post);
                }
              });
            } else if (state.status.isFailure) {
              context.showSnackbar(message: state.errorMessage ?? "");
            }

            // Handle image upload errors
            if (state.imageUploadStatus.isFailure) {
              context.showSnackbar(
                message: state.imageUploadError ?? "Image upload failed",
              );
            }
          },
          builder: (context, state) {
            if (!_initialized && widget.post != null) {
              _postController.text = widget.post!.content ?? '';
              _initialized = true;
            }
            final contextBloc = context;
            final isEnabled =
                _postController.text.isNotEmpty && !state.status.isLoading;
            return BaseScaffold(
              automaticallyImplyLeading: false,
              resizeToAvoidBottomInset: true,
              title: CreatePostAppBar(
                isUpdate: widget.post != null,
                onClose: () => Navigator.of(context).pop(),
                onPost: () {
                  // if (_postController.text.isEmpty) {
                  //   context.showSnackbar(
                  //     message: "Post content cannot be empty",
                  //   );
                  //   return;
                  // }
                  if (widget.post != null) {
                    context.read<CreatePostCubit>().updatePost(
                      id: widget.post!.id,
                      content: _postController.text,
                      privacy: state.privacy.apiValue,
                    );
                  } else {
                    context.read<CreatePostCubit>().createAPost(
                      content: _postController.text,
                    );
                  }
                },
                isLoading: state.status.isLoading,
                isEnabled: isEnabled,
              ),
              body: SafeArea(child: _body(contextBloc)),
            );
          },
        ),
      ),
    );
  }
}

void showPrivacyBottomSheet(BuildContext context) {
  final cubit = context.read<CreatePostCubit>();
  final privacy = cubit.state.privacy;
  showTtBottomSheet(
    context,
    child: PrivacyContent(
      selected: privacy,
      onChanged: (value) {
        cubit.setPrivacy(value);
        Navigator.of(context).pop();
      },
    ),
    isShowClose: true,
    isDismissible: true,
  );
}
