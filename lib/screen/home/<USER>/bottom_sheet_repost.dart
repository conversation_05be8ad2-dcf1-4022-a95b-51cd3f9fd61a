import 'package:flutter/material.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class BottomSheetRepost extends StatelessWidget {
  const BottomSheetRepost({super.key, this.onRepost, this.onQuote});
  final VoidCallback? onRepost;
  final VoidCallback? onQuote;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Content
        Column(
          children: [
            _RepostActionItem(
              icon: Assets.icons.icRepost.svg(
                width: 24,
                height: 24,
                colorFilter: ColorFilter.mode(
                  themeData.neutral300,
                  BlendMode.srcIn,
                ),
              ),
              text: 'Repost',
              onTap: () {
                Navigator.pop(context);
                onRepost?.call();
              },
            ),
            const SizedBox(height: 16),
            _RepostActionItem(
              icon: Assets.icons.icEdit.svg(
                width: 24,
                height: 24,
                colorFilter: ColorFilter.mode(
                  themeData.neutral300,
                  BlendMode.srcIn,
                ),
              ),
              text: 'Quote',

              onTap: () {
                Navigator.pop(context);
                onQuote?.call();
              },
            ),
          ],
        ),
      ],
    );
  }
}

class _RepostActionItem extends StatelessWidget {
  final Widget icon;
  final String text;
  final VoidCallback onTap;

  const _RepostActionItem({
    required this.icon,
    required this.text,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Row(
        children: [
          icon,
          const SizedBox(width: 16),
          Text(
            text,
            style: titleMedium.copyWith(
              color: themeData.neutral800,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
