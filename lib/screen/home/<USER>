import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/cubit/post/home/<USER>';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/home/<USER>/home_appbar_widget.dart';
import 'package:toii_social/screen/home/<USER>/home_item_nft_showcase_widget.dart';
import 'package:toii_social/screen/home/<USER>/home_item_post_widget.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/pull_refresh/app_refresh_view.dart';

class HomeMainPage extends StatefulWidget {
  final ScrollController _scrollController;
  const HomeMainPage({super.key, required ScrollController scrollController})
    : _scrollController = scrollController;

  @override
  State<HomeMainPage> createState() => _HomeMainPageState();
}

class _HomeMainPageState extends State<HomeMainPage>
    with AutomaticKeepAliveClientMixin {
  final _homeCubit = GetIt.instance<HomeCubit>();

  @override
  bool get wantKeepAlive => true;
  @override
  Widget build(BuildContext context) {
    super.build(context);

    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: _homeCubit..getUserFeed()),
        BlocProvider.value(value: GetIt.instance<ProfileCubit>()..getProfile()),
      ],
      child: Scaffold(
        backgroundColor: themeData.neutral50,
        body: BlocBuilder<HomeCubit, HomeState>(
          builder: (context, state) {
            return PullToRefreshView(
              onRefresh: () async {
                context.read<HomeCubit>().getUserFeed();
              },
              child: CustomScrollView(
                controller: widget._scrollController,
                slivers: [
                  SliverLayoutBuilder(
                    builder: (BuildContext context, constraints) {
                      final scrolled = constraints.scrollOffset > 0;

                      return SliverAppBar(
                        backgroundColor:
                            scrolled ? Colors.transparent : Colors.transparent,
                        shadowColor: Colors.transparent,
                        pinned: true,
                        snap: false,
                        automaticallyImplyLeading: false,
                        //   forceMaterialTransparency: true,
                        floating: true,
                        expandedHeight: 84,
                        centerTitle: false,
                        flexibleSpace:
                            scrolled
                                ? ClipRect(
                                  child: BackdropFilter(
                                    filter: ImageFilter.blur(
                                      sigmaX: 100,
                                      sigmaY: 100,
                                    ),
                                    child: spaceBar(scrolled),
                                  ),
                                )
                                : spaceBar(scrolled),
                        actions: [
                          Assets.icons.icNotification.svg(),
                          const SizedBox(width: 8),
                          GestureDetector(
                            onTap: () {
                              context.push(RouterEnums.ai.routeName);
                            },
                            child: SvgPicture.asset(
                              Assets.icons.icHomeAi.path,
                              // colorFilter: ColorFilter.mode(
                              //   themeData.primaryGreen500,
                              //   BlendMode.srcIn,
                              // ),
                            ),
                          ),
                          const SizedBox(width: 16),
                        ],
                      );
                    },
                  ),
                  const SliverToBoxAdapter(child: HomeItemNftShowcaseWidget()),
                  SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) => HomeItemPostWidget(
                        post: state.posts[index],
                        isShowActionMore: true,
                        isNotOnTap: false,
                      ),
                      childCount: state.posts.length,
                    ),
                  ),
                  // const SliverToBoxAdapter(child: HomeItemOneImageWidget()),
                  // const SliverToBoxAdapter(child: HomeItemNftShowcaseWidget()),
                  // const SliverToBoxAdapter(child: HomeItemOneCommentWidget()),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
