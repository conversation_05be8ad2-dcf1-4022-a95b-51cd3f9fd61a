import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/comment/comment_model.dart';
import 'package:toii_social/screen/home/<USER>/widget/bottom_sheet_reply_comment.dart';
import 'package:toii_social/screen/home/<USER>/bottom_sheet_report.dart';
import 'package:toii_social/widget/bottom_sheet/tt_bottom_sheet.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';

class BottomSheetMoreActionComment extends StatelessWidget {
  final CommentItemModel commentItemModel;
  final Function(List<ReportReason> reasons, String? customReason)? onReport;
  final VoidCallback? onReply;
  final VoidCallback? onHide;
  final VoidCallback? onFollow;
  final VoidCallback? onCopy;

  const BottomSheetMoreActionComment({
    super.key,
    required this.commentItemModel,
    this.onReport,
    this.onReply,
    this.onHide,
    this.onFollow,
    this.onCopy,
  });

  void _handleReport(BuildContext context) {
    Navigator.pop(context);
    showReportBottomSheet(
      context: context,
      commentId: commentItemModel.id,
      onReport: onReport,
    );
  }

  void _handleReply(BuildContext context) {
    Navigator.pop(context);
    showReplyCommentBottomSheet(
      context: context,
      commentItemModel: commentItemModel,
      onReply: (replyText) {
        // Handle reply submission
        print('Reply submitted: $replyText');
        onReply?.call();
      },
    );
  }

  void _handleHide(BuildContext context) {
    Navigator.pop(context);
    onHide?.call();
  }

  void _handleFollow(BuildContext context) {
    Navigator.pop(context);
    onFollow?.call();
  }

  void _handleCopy(BuildContext context) {
    Clipboard.setData(ClipboardData(text: commentItemModel.content));
    Navigator.pop(context);
    context.showSnackbar(
      message: 'Text copied to clipboard',
      duration: const Duration(seconds: 2),
    );
    onCopy?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: themeData.schemesOnPrimary,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Reply
          _buildActionItem(
            context: context,
            icon: Assets.icons.icReply.svg(),
            title: 'Reply',
            onTap: () => _handleReply(context),
          ),

          // Hide
          _buildActionItem(
            context: context,
            icon: Assets.icons.icCloseSquare.svg(),
            title: 'Hide',
            onTap: () => _handleHide(context),
          ),

          // Follow Comment
          _buildActionItem(
            context: context,
            icon: Assets.icons.icProfile2user.svg(),
            title: 'Follow Comment',
            onTap: () => _handleFollow(context),
          ),

          // Report comment
          _buildActionItem(
            context: context,
            icon: Assets.icons.icWarning.svg(),
            title: 'Report comment',
            onTap: () => _handleReport(context),
          ),

          // Copy text
          _buildActionItem(
            context: context,
            icon: Assets.icons.icCopy.svg(),
            title: 'Copy text',
            onTap: () => _handleCopy(context),
          ),
        ],
      ),
    );
  }

  Widget _buildActionItem({
    required BuildContext context,
    required Widget icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Row(
          children: [
            SizedBox(width: 24, height: 24, child: icon),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: titleMedium.copyWith(
                  color: themeData.neutral800,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Helper function to show the more action bottom sheet for comment
Future<void> showMoreActionCommentBottomSheet({
  required BuildContext context,
  required CommentItemModel commentItemModel,
  Function(List<ReportReason> reasons, String? customReason)? onReport,
  VoidCallback? onReply,
  VoidCallback? onHide,
  VoidCallback? onFollow,
  VoidCallback? onCopy,
}) {
  return showTtBottomSheet(
    context,
    child: BottomSheetMoreActionComment(
      commentItemModel: commentItemModel,
      onReport: onReport,
      onReply: onReply,
      onHide: onHide,
      onFollow: onFollow,
      onCopy: onCopy,
    ),
  );
}
