import 'package:flutter/material.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/text_field.dart/text_field.dart';

class CreatePostInput extends StatelessWidget {
  final TextEditingController controller;
  final String? avatarUrl;
  final String hintText;
  const CreatePostInput({
    super.key,
    required this.controller,
    this.avatarUrl,
    this.hintText = "What’s on your mind?",
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 44,
          height: 44,
          padding: const EdgeInsets.all(2),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: Theme.of(context).colorScheme.primary,
              width: 2,
            ),
          ),
          child: CircleAvatar(
            radius: 20,
            backgroundImage: AssetImage(
              avatarUrl ?? 'assets/images/avatar_sample.png',
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: TText<PERSON>ield(
            textController: controller,
            hintText: hintText,
            maxLines: 4,
            hintStyle: bodyLarge.copyColor(themeData.neutral400),
            textStyle: bodyLarge.copyColor(themeData.neutral800),
            autofocus: false,
            showBorder: false,
            // Các thuộc tính khác nếu cần thiết
          ),
        ),
      ],
    );
  }
}
