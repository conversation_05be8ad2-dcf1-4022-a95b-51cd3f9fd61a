import 'package:flutter/material.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/comment/comment_model.dart';
import 'package:toii_social/widget/bottom_sheet/tt_bottom_sheet.dart';
import 'package:toii_social/widget/button/button.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/images/avatar_widget.dart';
import 'package:toii_social/widget/text_field.dart/text_field.dart';

class BottomSheetReplyComment extends StatefulWidget {
  final CommentItemModel commentItemModel;
  final Function(String replyText)? onReply;

  const BottomSheetReplyComment({
    super.key,
    required this.commentItemModel,
    this.onReply,
  });

  @override
  State<BottomSheetReplyComment> createState() =>
      _BottomSheetReplyCommentState();
}

class _BottomSheetReplyCommentState extends State<BottomSheetReplyComment> {
  final TextEditingController _replyController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // Auto focus when bottom sheet opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _replyController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _handleSend() {
    final replyText = _replyController.text.trim();
    if (replyText.isNotEmpty) {
      widget.onReply?.call(replyText);
      Navigator.pop(context);
    }
  }

  void _handleCancel() {
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    final bottomInset = MediaQuery.of(context).viewInsets.bottom;

    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Padding(
        padding: EdgeInsets.only(bottom: bottomInset),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'Replying to ${widget.commentItemModel.user?.username ?? widget.commentItemModel.user?.fullName ?? "User"}',
                      style: titleMedium.copyWith(
                        color: themeData.neutral500,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  TSButton(
                    title: "Cancel",
                    size: ButtonSize.small,
                    elevatedVariant: ButtonVariant.tertiary,
                    isEnabled: true,
                    onPressed: _handleCancel,
                  ),
                ],
              ),
            ),

            // Divider
            Container(height: 1, color: themeData.neutral100),

            // Original comment
            Container(
              padding: const EdgeInsets.fromLTRB(12, 12, 32, 12),
              child: Row(
                children: [
                  AvatarWidget(
                    size: 32,
                    name:
                        widget.commentItemModel.user?.username ??
                        widget.commentItemModel.user?.fullName ??
                        "",
                    imageUrl: widget.commentItemModel.user?.avatar ?? "",
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      widget.commentItemModel.content,
                      style: bodyMedium.copyWith(color: themeData.neutral800),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),

            // Divider
            Container(height: 1, color: themeData.neutral100),

            // Reply input area
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // Picture icon
                  GestureDetector(
                    onTap: () {
                      // Handle picture selection
                    },
                    child: SizedBox(
                      width: 24,
                      height: 24,
                      child: Assets.icons.icPicture.svg(
                        colorFilter: ColorFilter.mode(
                          themeData.neutral500,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // Text field
                  Expanded(
                    child: TTextField(
                      textController: _replyController,
                      hintText: 'Write a reply...',
                      focusNode: _focusNode,
                      maxLines: 3,
                      contentPadding: const EdgeInsets.all(12),
                      onEditingComplete: _handleSend,
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Send button
                  GestureDetector(
                    onTap: _handleSend,
                    child: Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: themeData.primaryGreen500.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Center(
                        child: Assets.icons.icSend.svg(
                          width: 16,
                          height: 16,
                          colorFilter: ColorFilter.mode(
                            themeData.primaryGreen500,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Helper function to show the reply comment bottom sheet
Future<void> showReplyCommentBottomSheet({
  required BuildContext context,
  required CommentItemModel commentItemModel,
  Function(String replyText)? onReply,
}) {
  return showTtBottomSheet(
    context,
    child: BottomSheetReplyComment(
      commentItemModel: commentItemModel,
      onReply: onReply,
    ),
  );
}
