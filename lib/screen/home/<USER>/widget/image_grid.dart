import 'dart:io';

import 'package:flutter/material.dart';
import 'package:toii_social/screen/home/<USER>/detail_preview_img.dart';
import 'package:toii_social/widget/images/cached_network_image_widget.dart';

class ImageGrid extends StatelessWidget {
  final List<String> imagePaths;
  final List<String> imageUrls;
  final void Function(int index, bool isLocal) onRemove;

  const ImageGrid({
    super.key,
    required this.imagePaths,
    this.imageUrls = const [],
    required this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    final combinedImagesCount = imagePaths.length + imageUrls.length;
    if (combinedImagesCount == 0) return const SizedBox.shrink();

    List<Widget> rows = [];
    int i = 0;

    // First, add rows for remote images
    while (i < imageUrls.length) {
      if (i == imageUrls.length - 1 && imagePaths.isEmpty) {
        rows.add(
          Row(children: [Expanded(child: _buildRemoteImageItem(context, i))]),
        );
        break;
      } else if (i + 1 < imageUrls.length) {
        rows.add(
          Row(
            children: [
              Expanded(child: _buildRemoteImageItem(context, i)),
              const SizedBox(width: 4),
              Expanded(child: _buildRemoteImageItem(context, i + 1)),
            ],
          ),
        );
        i += 2;
      } else {
        // Last odd remote image, check if we have local images to pair with
        if (imagePaths.isNotEmpty) {
          rows.add(
            Row(
              children: [
                Expanded(child: _buildRemoteImageItem(context, i)),
                const SizedBox(width: 4),
                Expanded(child: _buildLocalImageItem(context, 0)),
              ],
            ),
          );
          i++;
          if (imagePaths.length == 1) break;

          // Continue with local images
          i = 1; // Start from the second local image
          while (i < imagePaths.length) {
            if (i == imagePaths.length - 1) {
              rows.add(
                Row(
                  children: [Expanded(child: _buildLocalImageItem(context, i))],
                ),
              );
              break;
            } else {
              rows.add(
                Row(
                  children: [
                    Expanded(child: _buildLocalImageItem(context, i)),
                    const SizedBox(width: 4),
                    Expanded(child: _buildLocalImageItem(context, i + 1)),
                  ],
                ),
              );
              i += 2;
            }
          }
          break;
        } else {
          rows.add(
            Row(children: [Expanded(child: _buildRemoteImageItem(context, i))]),
          );
          i++;
          break;
        }
      }
    }

    // If we only have local images
    if (imageUrls.isEmpty) {
      i = 0;
      while (i < imagePaths.length) {
        if (i == imagePaths.length - 1) {
          rows.add(
            Row(children: [Expanded(child: _buildLocalImageItem(context, i))]),
          );
          break;
        }
        rows.add(
          Row(
            children: [
              Expanded(child: _buildLocalImageItem(context, i)),
              const SizedBox(width: 4),
              if (i + 1 < imagePaths.length)
                Expanded(child: _buildLocalImageItem(context, i + 1)),
            ],
          ),
        );
        i += 2;
      }
    }

    return Column(
      children: [
        for (int j = 0; j < rows.length; j++) ...[
          rows[j],
          if (j != rows.length - 1) const SizedBox(height: 4),
        ],
      ],
    );
  }

  Widget _buildLocalImageItem(BuildContext context, int index) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (_) => DetailPreviewImg(
                  arg: DetailPreviewImgArg(
                    images: imagePaths,
                    initialIndex: index,
                    post: null,
                  ),
                ),
          ),
        );
      },
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: AspectRatio(
              aspectRatio: 1,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  image: DecorationImage(
                    image: FileImage(File(imagePaths[index])),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
          ),
          Positioned(
            top: 8,
            right: 8,
            child: GestureDetector(
              onTap: () => onRemove(index, true),
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: Colors.black54,
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.close, color: Colors.white, size: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRemoteImageItem(BuildContext context, int index) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (_) => DetailPreviewImg(
                  arg: DetailPreviewImgArg(
                    images: imageUrls,
                    initialIndex: index,
                    post: null,
                  ),
                ),
          ),
        );
      },
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: AspectRatio(
              aspectRatio: 1,
              child: CachedNetworkImageWidget(
                imageUrl: imageUrls[index],
                fit: BoxFit.cover,
              ),
            ),
          ),
          Positioned(
            top: 8,
            right: 8,
            child: GestureDetector(
              onTap: () => onRemove(index, false),
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: Colors.black54,
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.close, color: Colors.white, size: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
