import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_social/cubit/post/like_comment/like_comment_cubit.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/comment/comment_model.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class LikeCommentWidget extends StatelessWidget {
  const LikeCommentWidget({super.key, required this.commentItemModel});
  final CommentItemModel commentItemModel;

  @override
  Widget build(BuildContext context) {
    return BlocProvider<LikeCommentCubit>(
      create:
          (_) => LikeCommentCubit(
            commentId: commentItemModel.id,
            initialLiked: commentItemModel.isLiked ?? false,
            initialLikeCount: commentItemModel.actionlikes ?? 0,
          ),
      child: <PERSON><PERSON>uilder<LikeCommentCubit, LikeCommentState>(
        builder: (context, state) {
          return Column(
            children: [
              GestureDetector(
                onTap: () => context.read<LikeCommentCubit>().toggleLike(),
                child:
                    state.isLiked
                        ? Assets.icons.icHeartFilled.svg(width: 20, height: 20)
                        : Assets.icons.icHeart.svg(
                          width: 20,
                          height: 20,
                          color: themeData.neutral400,
                        ),
              ),
              const SizedBox(width: 4),
              Text(
                state.likeCount.toString(),
                style: labelSmall.copyWith(color: themeData.neutral300),
              ),
            ],
          );
        },
      ),
    );
  }
}
