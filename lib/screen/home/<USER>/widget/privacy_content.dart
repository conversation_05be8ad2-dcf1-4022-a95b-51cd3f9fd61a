import 'package:flutter/material.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/screen/home/<USER>/enum/privacy_option_enum.dart';
import 'package:toii_social/widget/button/button.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class PrivacyContent extends StatefulWidget {
  final PrivacyOption selected;
  final ValueChanged<PrivacyOption>? onChanged;
  const PrivacyContent({
    super.key,
    this.selected = PrivacyOption.everyone,
    this.onChanged,
  });

  @override
  State<PrivacyContent> createState() => _PrivacyContentState();
}

class _PrivacyContentState extends State<PrivacyContent> {
  late PrivacyOption _selected;
  final List<PrivacyOption> _options = PrivacyOption.values;

  @override
  void initState() {
    super.initState();
    _selected = widget.selected;
  }

  void _onSelect(PrivacyOption opt) {
    setState(() => _selected = opt);
    widget.onChanged?.call(opt);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          'Who can see your post',
          style: headlineSmall.copyColor(themeData.neutral800),
        ),
        const SizedBox(height: 4),
        Text(
          'Control your audience – only share with who you want',
          style: labelLarge.copyColor(themeData.neutral400),
        ),
        const SizedBox(height: 16),
        ..._options.map((opt) {
          return ListTile(
            contentPadding: EdgeInsets.zero,
            leading: opt.icon,
            title: Text(
              opt.label,
              style: titleMedium.copyColor(themeData.neutral800),
            ),
            trailing: Radio<PrivacyOption>(
              value: opt,
              groupValue: _selected,
              activeColor: themeData.primaryGreen600,
              onChanged: (v) => _onSelect(v!),
            ),
            onTap: () => _onSelect(opt),
          );
        }),
        const SizedBox(height: 16),
        TSButton(
          title: "Cancel",
          size: ButtonSize.block,
          elevatedVariant: ButtonVariant.black,
          onPressed: () => Navigator.pop(context),
        ),
      ],
    );
  }
}
