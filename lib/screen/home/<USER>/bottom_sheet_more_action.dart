import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/screen/home/<USER>/bottom_sheet_report.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';

class BottomSheetMoreAction extends StatelessWidget {
  const BottomSheetMoreAction({
    super.key,
    this.onEdit,
    this.onDelete,
    this.onHide,
    this.onReport,
    this.postText = '',
    this.postId,
    required this.isPostByUser,
  });

  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onHide;
  final Function(List<ReportReason> reasons, String? customReason)? onReport;

  final String postText;
  final String? postId;
  final bool isPostByUser;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(height: 12),
        if (isPostByUser) ...[
          _ActionItem(
            icon: Assets.icons.icEdit.svg(),
            text: 'Edit',
            onTap: () {
              Navigator.pop(context);
              onEdit?.call();
            },
          ),
          _ActionItem(
            icon: Assets.icons.icTrash.svg(),
            text: 'Delete',
            onTap: () {
              Navigator.pop(context);
              onDelete?.call();
            },
          ),
        ],
        _ActionItem(
          icon: Assets.icons.icCloseSquare.svg(),
          text: 'Hide post for me',
          onTap: () {
            Navigator.pop(context);
            onHide?.call();
          },
        ),
        if (!isPostByUser)
          _ActionItem(
            icon: Assets.icons.icWarning.svg(),
            text: 'Report post',
            onTap: () {
              Navigator.pop(context);
              showReportBottomSheet(
                context: context,
                postId: postId,
                onReport: (reasons, customReason) {
                  onReport?.call(reasons, customReason);
                },
              );
            },
          ),
        _ActionItem(
          icon: Assets.icons.icCopy.svg(),
          text: 'Copy post text',
          hint: 'Tap to copy the entire post content to the clipboard.',
          onTap: () {
            Navigator.pop(context);
            // Copy text to clipboard
            Clipboard.setData(ClipboardData(text: postText)).then((_) {
              // Show success message using the app's SnackBar extension
              context.showSnackbar(
                message: 'Post content copied to clipboard',
                duration: const Duration(seconds: 2),
              );
            });
          },
        ),

        const SizedBox(height: 12),
      ],
    );
  }
}

class _ActionItem extends StatelessWidget {
  final Widget icon;
  final String text;
  final String? hint;
  final VoidCallback onTap;

  const _ActionItem({
    required this.icon,
    required this.text,
    this.hint,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: hint ?? '',
      preferBelow: true,
      showDuration: const Duration(seconds: 2),
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: Row(
            children: [
              icon,
              const SizedBox(width: 16),
              Text(text, style: titleMedium.copyColor(themeData.neutral800)),
            ],
          ),
        ),
      ),
    );
  }
}
