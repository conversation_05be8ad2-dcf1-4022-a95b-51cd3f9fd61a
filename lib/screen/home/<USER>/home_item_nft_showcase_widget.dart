import 'package:flutter/material.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class HomeItemNftShowcaseWidget extends StatelessWidget {
  const HomeItemNftShowcaseWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: themeData.primaryGreen50,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: themeData.primaryGreen500.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title Row
          Padding(
            padding: const EdgeInsets.all(16),
            child: RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: 'NFT ',
                    style: headlineSmall.copyColor(themeData.primaryGreen500),
                  ),
                  TextSpan(
                    text: 'Showcase',
                    style: titleMedium.copyColor(themeData.neutral800),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 12),
          _listNftShowcaseCard(),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _listNftShowcaseCard() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: SizedBox(
        height: 332,
        child: ListView.separated(
          padding: const EdgeInsets.symmetric(horizontal: 0),
          scrollDirection: Axis.horizontal,
          itemCount: 5, // Example count
          itemBuilder: (context, index) {
            return Assets.images.nftSample.image(width: 265, fit: BoxFit.cover);
          },
          separatorBuilder: (context, index) {
            return const SizedBox(width: 16);
          },
        ),
      ),
    );
  }
}

class NftShowcaseCard extends StatelessWidget {
  const NftShowcaseCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF2F5E8),
        borderRadius: BorderRadius.circular(24),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title Row
          const Padding(
            padding: EdgeInsets.only(bottom: 16),
            // child: RichText(
            //   text: TextSpan(
            //     style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
            //     children: [
            //       TextSpan(
            //         text: 'NFT ',
            //         style: TextStyle(color: Colors.green),
            //       ),
            //       TextSpan(
            //         text: 'Showcase',
            //         style: TextStyle(color: Colors.black),
            //       ),
            //     ],
            //   ),
            // ),
          ),

          // NFT Card
          SizedBox(
            height: 300,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: Stack(
                children: [
                  // NFT Image
                  Positioned.fill(
                    child: Image.network(
                      'https://example.com/robot-nft.jpg', // Replace with your NFT image
                      fit: BoxFit.cover,
                    ),
                  ),

                  // Heart Icon
                  Positioned(
                    top: 12,
                    right: 12,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.3),
                        shape: BoxShape.circle,
                      ),
                      padding: const EdgeInsets.all(8),
                      child: const Icon(
                        Icons.favorite_border,
                        color: Colors.white,
                      ),
                    ),
                  ),

                  // Bottom Info Overlay
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.6),
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(20),
                          bottomRight: Radius.circular(20),
                        ),
                      ),
                      child: Row(
                        children: [
                          // Avatar
                          const CircleAvatar(
                            radius: 12,
                            backgroundImage: NetworkImage(
                              'https://example.com/avatar.png', // Replace with actual avatar
                            ),
                          ),
                          const SizedBox(width: 8),

                          // Title and Creator
                          const Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Hedgie #2314',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 13,
                                ),
                              ),
                              Text(
                                'by Williamson',
                                style: TextStyle(
                                  color: Colors.white70,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
