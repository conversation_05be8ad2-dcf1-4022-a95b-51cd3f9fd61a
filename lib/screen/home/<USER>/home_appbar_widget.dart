import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/images/avatar_widget.dart';

FlexibleSpaceBar spaceBar(bool scrolled) {
  return FlexibleSpaceBar(
    expandedTitleScale: 1,
    collapseMode: CollapseMode.none,
    title: Padding(
      padding: const EdgeInsets.only(left: 16),
      child: BlocBuilder<ProfileCubit, ProfileState>(
        builder: (context, state) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Row(
                children: [
                  GradientBorderCircle(
                    imageUrl: state.userModel?.avatar,
                    name: state.userModel?.fullName ?? "",
                  ),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Welcome",
                        style: titleLarge.copyColor(themeData.primaryGreen500),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        state.userModel?.fullName ?? "",
                        style: titleSmall.copyColor(themeData.neutral800),
                      ),
                      const SizedBox(height: 16),
                    ],
                  ),
                ],
              ),
            ],
          );
        },
      ),
    ),
    centerTitle: false,
  );
}

class GradientBorderCircle extends StatelessWidget {
  final String name;
  final String? imageUrl;
  const GradientBorderCircle({
    super.key,
    this.imageUrl,
    this.name = "Robert Fox",
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 56,
      height: 56,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Outer container with gradient border
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [Color(0xFFB5E360), Color(0xFFFFFFFF)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          // Inner container with solid color (to simulate border thickness)
          Container(
            width: 56 - 4, // subtract 2px border from both sides
            height: 56 - 4,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white, // background or fill color
            ),
            child: AvatarWidget(imageUrl: imageUrl, name: name),
          ),
        ],
      ),
    );
  }
}
