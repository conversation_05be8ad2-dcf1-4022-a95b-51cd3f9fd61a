import 'package:country_code_picker/country_code_picker.dart';
import 'package:flutter/material.dart';
import 'package:toii_social/widget/bottom_sheet/tt_bottom_sheet.dart';

class BottomSheetCountry extends StatefulWidget {
  final List<CountryCode> elements;
  final bool? showCountryOnly;
  final InputDecoration searchDecoration;
  final TextStyle? searchStyle;
  final TextStyle? textStyle;
  final BoxDecoration? boxDecoration;
  final WidgetBuilder? emptySearchBuilder;
  final double flagWidth;
  final Decoration? flagDecoration;
  final Size? size;

  /// Background color of BottomSheetCountry
  final Color? backgroundColor;

  /// Boxshaow color of BottomSheetCountry that matches CountryCodePicker barrier color
  final Color? barrierColor;

  /// elements passed as favorite
  final List<CountryCode> favoriteElements;

  BottomSheetCountry(
    this.elements,
    this.favoriteElements, {
    Key? key,
    this.showCountryOnly,
    this.emptySearchBuilder,
    InputDecoration searchDecoration = const InputDecoration(),
    this.searchStyle,
    this.textStyle,
    this.boxDecoration,
    this.flagDecoration,
    this.flagWidth = 32,
    this.size,
    this.backgroundColor,
    this.barrierColor,
  }) : searchDecoration =
           searchDecoration.prefixIcon == null
               ? searchDecoration.copyWith(prefixIcon: const Icon(Icons.search))
               : searchDecoration,
       super(key: key);

  @override
  State<StatefulWidget> createState() => _BottomSheetCountryState();
}

class _BottomSheetCountryState extends State<BottomSheetCountry> {
  /// this is useful for filtering purpose
  late List<CountryCode> filteredElements;

  @override
  Widget build(BuildContext context) => Padding(
    padding: const EdgeInsets.all(0.0),
    child: SizedBox(
      width: widget.size?.width ?? MediaQuery.of(context).size.width,
      height: widget.size?.height ?? MediaQuery.of(context).size.height * 0.85,

      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: TextField(
              style: widget.searchStyle,
              decoration: widget.searchDecoration,
              onChanged: _filterElements,
            ),
          ),
          Expanded(
            child: ListView(
              children: [
                widget.favoriteElements.isEmpty
                    ? const DecoratedBox(decoration: BoxDecoration())
                    : Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ...widget.favoriteElements.map(
                          (f) => SimpleDialogOption(
                            child: _buildOption(f),
                            onPressed: () {
                              _selectItem(f);
                            },
                          ),
                        ),
                        const Divider(),
                      ],
                    ),
                if (filteredElements.isEmpty)
                  _buildEmptySearchWidget(context)
                else
                  ...filteredElements.map(
                    (e) => SimpleDialogOption(
                      child: _buildOption(e),
                      onPressed: () {
                        _selectItem(e);
                      },
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    ),
  );

  Widget _buildOption(CountryCode e) {
    return Container(
      height: 44,
      margin: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                SizedBox(
                  width: 24,
                  height: 24,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(100),
                    child: Image.asset(
                      e.flagUri!,
                      fit: BoxFit.cover,
                      package: 'country_code_picker',
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Flexible(
                  child: Text(
                    e.toCountryStringOnly(),
                    overflow: TextOverflow.fade,
                    style: widget.textStyle,
                  ),
                ),
              ],
            ),
          ),
          Text(
            e.dialCode ?? "",
            overflow: TextOverflow.fade,
            style: widget.textStyle,
          ),
          const SizedBox(width: 16),
        ],
      ),
    );
  }

  Widget _buildEmptySearchWidget(BuildContext context) {
    if (widget.emptySearchBuilder != null) {
      return widget.emptySearchBuilder!(context);
    }

    return Center(
      child: Text(
        CountryLocalizations.of(context)?.translate('no_country') ??
            'No country found',
      ),
    );
  }

  @override
  void initState() {
    filteredElements = widget.elements;
    super.initState();
  }

  void _filterElements(String s) {
    s = s.toUpperCase();
    setState(() {
      filteredElements =
          widget.elements
              .where(
                (e) =>
                    e.code!.contains(s) ||
                    e.dialCode!.contains(s) ||
                    e.name!.toUpperCase().contains(s),
              )
              .toList();
    });
  }

  void _selectItem(CountryCode e) {
    Navigator.pop(context, e);
  }
}

Future<dynamic> showBottomSheetCountry({required BuildContext context}) async {
  List<Map<String, String>> jsonList = codes;

  List<CountryCode> elements =
      jsonList.map((json) => CountryCode.fromJson(json)).toList();
  List<CountryCode> favoriteElements =
      elements.where((e) => e.code == 'US' || e.dialCode == '+84').toList();
  final result = await showTtBottomSheet(
    context,
    child: BottomSheetCountry(
      elements,
      favoriteElements,
      showCountryOnly: false,
   
      flagWidth: 32,
    ),
  );
  return result;
}
