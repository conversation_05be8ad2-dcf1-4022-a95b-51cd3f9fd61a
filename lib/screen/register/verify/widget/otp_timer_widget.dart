import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_social/cubit/auth/register/request_otp/register_request_otp_cubit.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/generated/l10n.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';

class OtpTimerWidget extends StatefulWidget {
  final VoidCallback? onResend;
  const OtpTimerWidget({super.key, this.onResend});

  @override
  State<OtpTimerWidget> createState() => _OtpTimerWidgetState();
}

class _OtpTimerWidgetState extends State<OtpTimerWidget> {
  final interval = const Duration(seconds: 1);
  Timer? _timer;
  bool isShowResend = false;
  final int timerMaxSeconds = 60;

  int currentSeconds = 0;

  String get timerText =>
      '${((timerMaxSeconds - currentSeconds) ~/ 60).toString().padLeft(2, '0')}: ${((timerMaxSeconds - currentSeconds) % 60).toString().padLeft(2, '0')}';

  startTimeout([int? milliseconds]) {
    var duration = interval;
    _timer?.cancel();
    _timer = Timer.periodic(duration, (timer) {
      setState(() {
        currentSeconds = timer.tick;
        if (currentSeconds == timerMaxSeconds) {
          isShowResend = true;
        }
        if (timer.tick >= timerMaxSeconds) timer.cancel();
      });
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  void initState() {
    startTimeout();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<RegisterRequestOtpCubit, RegisterRequestOtpState>(
      listener: (context, state) {
        if (state.status.isSuccess) {
          startTimeout();
        } else if (state.status.isFailure) {
          setState(() {
            isShowResend = true;
          });
          context.showSnackbar(message: state.errorMessage!);
        }
      },
      child: Column(
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Text(
                timerText,
                style: bodyMedium.copyColor(themeData.neutral800),
              ),
            ],
          ),
          const SizedBox(height: 8),
          if (isShowResend)
            GestureDetector(
              onTap: () {
                setState(() {
                  isShowResend = false;
                });
                widget.onResend?.call();
              },
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    S.current.register_didnt_receive_code,
                    style: labelLarge.copyColor(themeData.neutral400),
                  ),
                  const SizedBox(width: 2),
                  Text(
                    S.current.register_resend_code,
                    style: labelLarge.copyColor(themeData.primaryGreen500),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
