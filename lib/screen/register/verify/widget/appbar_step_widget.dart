import 'package:flutter/material.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/widget/colors/colors.dart';

class AppbarStepWidget extends StatelessWidget {
  final int step;
  const AppbarStepWidget({super.key, required this.step});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 200,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Container(
            width: 9,
            height: 9,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(1000),
              color: themeData.primaryGreen500,
            ),
          ),
          Expanded(
            child: Container(
              height: 1,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(1000),
                color:
                    step > 1
                        ? themeData.primaryGreen500
                        : themeData.primaryGreen100,
              ),
            ),
          ),
          Container(
            width: 9,
            height: 9,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(1000),
              color:
                  step >= 2
                      ? themeData.primaryGreen500
                      : themeData.primaryGreen100,
            ),
          ),
          Expanded(
            child: Container(
              height: 1,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(1000),
                color:
                    step >= 3
                        ? themeData.primaryGreen500
                        : themeData.primaryGreen100,
              ),
            ),
          ),
          Container(
            width: 9,
            height: 9,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(1000),
              color:
                  step >= 3
                      ? themeData.primaryGreen500
                      : themeData.primaryGreen100,
            ),
          ),
        ],
      ),
    );
  }
}
