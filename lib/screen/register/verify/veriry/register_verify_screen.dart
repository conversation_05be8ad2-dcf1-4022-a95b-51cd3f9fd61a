import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_social/cubit/auth/register/register_verify_step/register_verify_step_cubit.dart';
import 'package:toii_social/screen/register/verify/widget/appbar_step_widget.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';

enum RegisterVerifyType {
  verifyOTP,
  createName,
  createPassword,
  createPin,
  createWallet,
}

class RegisterVerifyScreen extends StatefulWidget {
  final Widget child;

  const RegisterVerifyScreen({super.key, required this.child});

  @override
  State<RegisterVerifyScreen> createState() => _RegisterVerifyScreenState();
}

class _RegisterVerifyScreenState extends State<RegisterVerifyScreen> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => RegisterVerifyStepCubit(),
      child: BaseScaffold(
        showLeading: true,
        title: BlocBuilder<RegisterVerifyStepCubit, int>(
          builder: (context, state) {
            return AppbarStepWidget(step: state);
          },
        ),
        body: _bodyWidget(),
      ),
    );
  }

  Widget _bodyWidget() {
    return widget.child;
  }
}
