import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/auth/register/register_create_wallet/register_create_wallet_cubit.dart';
import 'package:toii_social/cubit/auth/register/register_verify_step/register_verify_step_cubit.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/generated/l10n.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/utils/widget/dismiss_keyboard_widget.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/loading/loading_indicator_widget.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';

class RegisterCreateWalletScreen extends StatefulWidget {
  const RegisterCreateWalletScreen({super.key});

  @override
  State<RegisterCreateWalletScreen> createState() =>
      _RegisterCreateWalletScreenState();
}

class _RegisterCreateWalletScreenState
    extends State<RegisterCreateWalletScreen> {
  @override
  Widget build(BuildContext context) {
    final info = context.read<RegisterVerifyStepCubit>().requestInfo;
   // final pin = context.read<RegisterVerifyStepCubit>().pin;

    return BlocProvider(
      create:
          (_) =>
              RegisterCreateWalletCubit(requestInfo: info)
                ..completeRegistration(),
      child: _bodyWidget(),
    );
  }

  Widget _bodyWidget() {
    return BlocListener<RegisterCreateWalletCubit, RegisterCreateWalletState>(
      listener: (context, state) {
        if (state.status.isSuccess) {
          context.go(RouterEnums.inital.routeName);
        } else if (state.status.isFailure) {
          context.showSnackbar(message: state.errorMessage!);
        }
      },
      child: DismissKeyboardWidget(
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      const SizedBox(height: 100),
                      SvgPicture.asset(Assets.icons.icToii.path),
                      const SizedBox(height: 32),
                      LoadingIndicatorWidget(),
                      const SizedBox(height: 16),
                      Text(
                        S.current.register_creating_wallet,
                        style: titleMedium.copyColor(themeData.neutral800),
                      ),

                      Expanded(child: Container()),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
