import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:pinput/pinput.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/generated/l10n.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/utils/widget/dismiss_keyboard_widget.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class RegisterCreatePinScreen extends StatefulWidget {
  const RegisterCreatePinScreen({super.key});

  @override
  State<RegisterCreatePinScreen> createState() =>
      _RegisterCreatePinScreenState();
}

class _RegisterCreatePinScreenState extends State<RegisterCreatePinScreen> {
  final _otpController = TextEditingController();
  bool isConfirmed = false;
  String pin = '';
  String? errorPin;
  final _focusNode = FocusNode();
  @override
  void dispose() {
    _otpController.dispose();
    super.dispose();
  }

  Widget _bodyWidget() {
    return DismissKeyboardWidget(
      child: Safe<PERSON>rea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            children: [
              Expanded(
                child: Column(
                  children: [
                    const SizedBox(height: 16),
                    Text(
                      S.current.register_create_wallet_title,
                      style: headlineSmall.copyColor(themeData.neutral800),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      isConfirmed
                          ? S.current.register_confirm_pin_title
                          : S.current.register_enter_pin_title,
                      style: headlineSmall.copyColor(themeData.neutral800),
                    ),
                    if (!isConfirmed) const SizedBox(height: 16),
                    if (!isConfirmed)
                      Text(
                        textAlign: TextAlign.center,
                        S.current.register_enter_pin_description,
                        style: bodyLarge.copyColor(themeData.neutral400),
                      ),

                    const SizedBox(height: 64),
                    _otpInputWidget(),
                    Expanded(child: Container()),
                  ],
                ),
              ),
              //   _bottomWidget(),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return _bodyWidget();
  }

  Widget _otpInputWidget() {
    final defaultPinTheme = PinTheme(
      width: 16,
      height: 16,
      textStyle: headlineMedium.copyColor(Colors.transparent),
      decoration: BoxDecoration(
        color: themeData.neutral200,
        shape: BoxShape.circle,
      ),
    );

    final filledPinTheme = defaultPinTheme.copyWith(
      decoration: BoxDecoration(
        color: themeData.primaryGreen500,
        shape: BoxShape.circle,
      ),
    );
    return Pinput(
      length: 6,
      obscureText: true,
      showCursor: false,
      focusNode: _focusNode,
      autofocus: true,
      keyboardType: TextInputType.number,
      controller: _otpController,
      defaultPinTheme: defaultPinTheme,
      followingPinTheme: defaultPinTheme,
      focusedPinTheme: defaultPinTheme,
      submittedPinTheme: filledPinTheme,
      errorText: errorPin,

      onChanged: (value) {
        setState(() {
          errorPin = null;
        });
      },
      onCompleted: (otp) {
        if (!isConfirmed) {
          pin = otp;
          setState(() {
            isConfirmed = true;
          });
          _otpController.clear();
          _focusNode.requestFocus();
        } else {
          if (pin == otp) {
            context.push(RouterEnums.registerCreateWallet.routeName);
          } else {
            setState(() {
              errorPin = S.current.register_create_pin_error;
            });
          }
        }
        // setState(() {
        //   _isEnabledButton = otp.length == 6;
        // });
      },
    );
  }
}
