import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:pinput/pinput.dart';
import 'package:toii_social/core/constant/enum_constant.dart';
import 'package:toii_social/cubit/auth/register/register_verify_step/register_verify_step_cubit.dart';
import 'package:toii_social/cubit/auth/register/request_otp/register_request_otp_cubit.dart';
import 'package:toii_social/cubit/auth/register/verify_otp/verify_otp_cubit.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/generated/l10n.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/register/verify/create_name/register_create_name_screen.dart';
import 'package:toii_social/screen/register/verify/widget/otp_timer_widget.dart';
import 'package:toii_social/widget/button/button.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';

extension RegisterTypeExtension on RegisterType {
  String get title {
    switch (this) {
      case RegisterType.email:
        return S.current.register_verify_email_title;
      case RegisterType.phone:
        return S.current.register_verify_phone_title;
      default:
        return '';
    }
  }
}

class RegisterVerifyPhoneArguments {
  final RegisterType registerType;
  final String? value;
  RegisterVerifyPhoneArguments({
    required this.registerType,
     this.value,
  });
}

class RegisterVerifyPhoneScreen extends StatefulWidget {
  final RegisterVerifyPhoneArguments arg;
  const RegisterVerifyPhoneScreen({super.key, required this.arg});

  @override
  State<RegisterVerifyPhoneScreen> createState() =>
      _RegisterVerifyPhoneScreenState();
}

class _RegisterVerifyPhoneScreenState extends State<RegisterVerifyPhoneScreen> {
  final _otpController = TextEditingController();
  bool _isEnabledButton = false;
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => VerifyOtpCubit()),
        BlocProvider(create: (_) => RegisterRequestOtpCubit()),
      ],
      child: _bodyWidget(),
    );
  }

  @override
  void dispose() {
    _otpController.dispose();
    super.dispose();
  }

  Widget _bottomWidget() {
    return BlocConsumer<VerifyOtpCubit, VerifyOtpState>(
      listener: (context, state) {
        if (state.status.isSuccess) {
          context.read<RegisterVerifyStepCubit>().updateRequestInfo(
            registerType:
                widget.arg.registerType == RegisterType.email
                    ? "email"
                    : "phone",
            email:
                widget.arg.registerType == RegisterType.email
                    ? widget.arg.value
                    : null,
            phoneNumber:
                widget.arg.registerType == RegisterType.phone
                    ? widget.arg.value
                    : null,
          );
          context.read<RegisterVerifyStepCubit>().nextStep();

          context.replace(RouterEnums.registerCreateName.routeName, extra: widget.arg.registerType);
        } else if (state.status.isFailure) {
          context.showSnackbar(message: state.errorMessage ?? "");
          // ScaffoldMessenger.of(context).showSnackBar(
          //   SnackBar(content: Text(state.message)),
          // );
        }
      },
      builder: (context, state) {
        return Container(
          margin: EdgeInsets.only(
            // left: 16,
            // right: 16,
            bottom: MediaQuery.of(context).padding.bottom + 8,
          ),
          child: TSButton.primary(
            title: S.current.register_confirm_button,
            isLoading: state.status.isLoading,
            isEnabled: _isEnabledButton,
            onPressed: () {
              context.read<VerifyOtpCubit>().verifyOtp(
                otp: _otpController.text,
                email:
                    widget.arg.registerType == RegisterType.email
                        ? widget.arg.value
                        : null,
                phoneNumber:
                    widget.arg.registerType == RegisterType.phone
                        ? widget.arg.value
                        : null,
              );
            },
          ),
        );
      },
    );
  }

  Widget _bodyWidget() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const SizedBox(height: 12),
                  Text(
                    widget.arg.registerType.title,
                    style: headlineSmall.copyColor(themeData.neutral800),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    S.current.register_verify_phone_subtitle,
                    style: bodyMedium.copyColor(themeData.neutral400),
                  ),
                  Text(
                    widget.arg.value ?? "",
                    style: labelLarge.copyColor(themeData.neutral800),
                  ),
                  const SizedBox(height: 48),
                  _otpInputWidget(),
                  const SizedBox(height: 16),
                  BlocBuilder<RegisterRequestOtpCubit, RegisterRequestOtpState>(
                    builder: (context, state) {
                      return OtpTimerWidget(
                        onResend: () {
                          if (widget.arg.registerType == RegisterType.email) {
                            context
                                .read<RegisterRequestOtpCubit>()
                                .requestOtpEmail(email: widget.arg.value ?? ""
                                );
                          } else {
                            context
                                .read<RegisterRequestOtpCubit>()
                                .requestOtpPhone(
                                  code: "",
                                  phoneNumber: widget.arg.value ?? "",
                                );
                          }
                        },
                      );
                    },
                  ),

                  Expanded(child: Container()),
                ],
              ),
            ),
            _bottomWidget(),
          ],
        ),
      ),
    );
  }

  Widget _otpInputWidget() {
    final defaultPinTheme = PinTheme(
      width: 56,
      height: 56,
      textStyle: headlineMedium.copyColor(themeData.neutral800),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: themeData.primaryGreen500, width: 2),
        ),
      ),
    );
    return Pinput(
      length: 6,
      autofocus: true,
      pinAnimationType: PinAnimationType.slide,
      controller: _otpController,
      defaultPinTheme: defaultPinTheme,
      onCompleted: (otp) {
        setState(() {
          _isEnabledButton = otp.length == 6;
        });
      },
      onChanged: (value) {
        setState(() {
          _isEnabledButton = value.length == 6;
        });
      },
      showCursor: true,
    );
  }
}
