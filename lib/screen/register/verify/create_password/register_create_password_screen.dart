import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/auth/register/register_create_password/create_password_validate_cubit.dart';
import 'package:toii_social/cubit/auth/register/register_verify_step/register_verify_step_cubit.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/generated/l10n.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/utils/bio/authentication_service.dart';
import 'package:toii_social/utils/widget/dismiss_keyboard_widget.dart';
import 'package:toii_social/widget/button/button.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/text_field.dart/text_field.dart';

class RegisterCreatePasswordScreen extends StatefulWidget {
  const RegisterCreatePasswordScreen({super.key});

  @override
  State<RegisterCreatePasswordScreen> createState() =>
      _RegisterCreatePasswordScreenState();
}

class _RegisterCreatePasswordScreenState
    extends State<RegisterCreatePasswordScreen> {
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final FocusNode _focusPassword = FocusNode();
  final FocusNode _focusConfirmPassword = FocusNode();

  final _createPasswordValidateCubit = CreatePasswordValidateCubit();
  var typeBiometric = Biometric.none;

  @override
  void initState() {
    super.initState();
    _focusPassword.addListener(_onFocusChangePassword);
    _focusConfirmPassword.addListener(_onFocusChangeConfirmPassword);
    _getStatusBiometric();
  }

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
    _focusPassword.removeListener(_onFocusChangePassword);
    _focusPassword.dispose();
    _focusConfirmPassword.removeListener(_onFocusChangeConfirmPassword);
    _focusConfirmPassword.dispose();
  }

  void _getStatusBiometric() async {
    final status = await AuthenticationService.typeBioMetric();
    setState(() {
      typeBiometric = status;
    });
  }

  void _onFocusChangePassword() {
    if (_focusPassword.hasFocus) {
      _createPasswordValidateCubit.clearErrorPassword();
    } else {
      _createPasswordValidateCubit.validateForm(
        password: _passwordController.text,
      );
    }
  }

  void _onFocusChangeConfirmPassword() {
    if (_focusConfirmPassword.hasFocus) {
      _createPasswordValidateCubit.clearErrorPassword();
    } else {
      _createPasswordValidateCubit.validateForm(
        confirmPassword: _confirmPasswordController.text,
        password: _passwordController.text,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => _createPasswordValidateCubit,
      child: _bodyWidget(),
    );
  }

  Widget _bottomWidget() {
    return BlocBuilder<
      CreatePasswordValidateCubit,
      CreatePasswordValidateState
    >(
      builder: (context, state) {
        return Container(
          margin: EdgeInsets.only(
            bottom: MediaQuery.of(context).padding.bottom + 8,
          ),
          child: TSButton.primary(
            title: S.current.register_create_password,
            isEnabled: state.isEnableButton,
            onPressed: () {
              context.read<RegisterVerifyStepCubit>().updateRequestInfo(
                password: _passwordController.text,
              );
              context.go(RouterEnums.registerCreatePin.routeName);
            },
          ),
          //  );
          //  },
        );
      },
    );
  }

  Widget _passwordFormWidget() {
    return BlocBuilder<
      CreatePasswordValidateCubit,
      CreatePasswordValidateState
    >(
      builder: (context, state) {
        return TTextField(
          prefixIcon: IconButton(
            icon: SvgPicture.asset(
              Assets.icons.icLock.path,
              colorFilter: ColorFilter.mode(
                themeData.neutral400,
                BlendMode.srcIn,
              ),
            ),
            onPressed: null,
          ),
          hintText: S.current.register_password_label,
          labelText: S.current.register_password_label,
          focusNode: _focusPassword,
          onChanged: (value) {
            _createPasswordValidateCubit.validateValidPassword(
              password: _passwordController.text,
              confirmPassword: _confirmPasswordController.text,
            );
          },
          onEditingComplete: () {
            _createPasswordValidateCubit.validateForm(
              password: _passwordController.text,
            );

            _focusPassword.unfocus();
          },
          obscureText: !state.isShowPassword,
          textController: _passwordController,
          errorText:
              state.errorPassword?.isNotEmpty == true &&
                      state.isEnableButton == false
                  ? state.errorPassword
                  : null,
          suffixIcon: IconButton(
            icon: SvgPicture.asset(
              state.isShowPassword
                  ? Assets.icons.eyeHide.path
                  : Assets.icons.eyeShow.path,
            ),
            onPressed: () {
              _createPasswordValidateCubit.toggleShowPassword();
            },
          ),
        );
      },
    );
  }

  Widget _passwordConfirmFormWidget() {
    return BlocBuilder<
      CreatePasswordValidateCubit,
      CreatePasswordValidateState
    >(
      builder: (context, state) {
        return TTextField(
          prefixIcon: IconButton(
            icon: SvgPicture.asset(
              Assets.icons.icLock.path,
              colorFilter: ColorFilter.mode(
                themeData.neutral400,
                BlendMode.srcIn,
              ),
            ),
            onPressed: null,
          ),
          hintText: S.current.register_confirm_password_label,
          labelText: S.current.register_confirm_password_label,
          focusNode: _focusConfirmPassword,
          onChanged: (value) {
            _createPasswordValidateCubit.validateValidPassword(
              password: _passwordController.text,
              confirmPassword: _confirmPasswordController.text,
            );
          },
          onEditingComplete: () {
            _createPasswordValidateCubit.validateForm(
              confirmPassword: _confirmPasswordController.text,
              password: _passwordController.text,
            );

            _focusConfirmPassword.unfocus();
          },
          obscureText: !state.isShowConfirmPassword,
          textController: _confirmPasswordController,
          errorText:
              state.errorConfirmPassword?.isNotEmpty == true &&
                      state.isEnableButton == false
                  ? state.errorConfirmPassword
                  : null,
          suffixIcon: IconButton(
            icon: SvgPicture.asset(
              state.isShowConfirmPassword
                  ? Assets.icons.eyeHide.path
                  : Assets.icons.eyeShow.path,
            ),
            onPressed: () {
              context
                  .read<CreatePasswordValidateCubit>()
                  .toggleShowConfirmPassword();
            },
          ),
        );
      },
    );
  }

  Widget _descriptionWidget() {
    return BlocBuilder<
      CreatePasswordValidateCubit,
      CreatePasswordValidateState
    >(
      // buildWhen:
      //     (previous, current) =>
      //         previous.have8Characters != current.have8Characters ||
      //         previous.containsUppercase != current.containsUppercase ||
      //         previous.haveSpecialCharacter != current.haveSpecialCharacter ||
      //         previous.isEnableButton != current.isEnableButton,
      builder: (context, state) {
        return SizedBox(
          width: double.infinity,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                S.current.register_password_requirements_title,
                style: titleSmall.copyColor(themeData.neutral800),
                textAlign: TextAlign.left,
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SvgPicture.asset(
                    state.have8Characters
                        ? Assets.icons.icChecked.path
                        : Assets.icons.icCheckbox.path,
                    width: 16,
                    height: 16,
                    colorFilter:
                        state.have8Characters
                            ? null
                            : ColorFilter.mode(
                              themeData.neutral200,
                              BlendMode.srcIn,
                            ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    S.current.register_password_rule_1,
                    style: bodySmall.copyColor(themeData.neutral400),
                    textAlign: TextAlign.left,
                  ),
                ],
              ),
              const SizedBox(height: 6),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SvgPicture.asset(
                    state.containsUppercase
                        ? Assets.icons.icChecked.path
                        : Assets.icons.icCheckbox.path,
                    width: 16,
                    height: 16,
                    colorFilter:
                        state.containsUppercase
                            ? null
                            : ColorFilter.mode(
                              themeData.neutral200,
                              BlendMode.srcIn,
                            ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    S.current.register_password_rule_2,
                    style: bodySmall.copyColor(themeData.neutral400),
                    textAlign: TextAlign.left,
                  ),
                ],
              ),
              const SizedBox(height: 6),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SvgPicture.asset(
                    state.haveSpecialCharacter
                        ? Assets.icons.icChecked.path
                        : Assets.icons.icCheckbox.path,
                    width: 16,
                    height: 16,
                    colorFilter:
                        state.haveSpecialCharacter
                            ? null
                            : ColorFilter.mode(
                              themeData.neutral200,
                              BlendMode.srcIn,
                            ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    S.current.register_password_rule_3,
                    style: bodySmall.copyColor(themeData.neutral400),
                    textAlign: TextAlign.left,
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _biometricWidget() {
    final WidgetStateProperty<Color?> trackColor =
        WidgetStateProperty<Color?>.fromMap(<WidgetStatesConstraint, Color>{
          WidgetState.selected: themeData.primaryGreen500,
        });
    if (typeBiometric == Biometric.none) {
      return const SizedBox.shrink();
    }
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          S.current.register_unlock_with_face_id,
          style: titleMedium.copyColor(themeData.neutral800),
        ),
        BlocBuilder<CreatePasswordValidateCubit, CreatePasswordValidateState>(
          buildWhen:
              (previous, current) =>
                  previous.isEnableBiometric != current.isEnableBiometric,
          builder: (context, state) {
            return Switch(
              value: state.isEnableBiometric,
              trackColor: trackColor,
              thumbColor: WidgetStatePropertyAll<Color>(
                themeData.schemesOnPrimary,
              ),
              onChanged: (bool value) {
                context.read<CreatePasswordValidateCubit>().toggleOnBiometric();
              },
            );
          },
        ),
      ],
    );
  }

  Widget _bodyWidget() {
    return DismissKeyboardWidget(
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            children: [
              Expanded(
                child: Column(
                  children: [
                    const SizedBox(height: 16),
                    Text(
                      S.current.register_create_password,
                      style: headlineSmall.copyColor(themeData.neutral800),
                    ),
                    const SizedBox(height: 16),
                    _passwordFormWidget(),
                    const SizedBox(height: 16),
                    _passwordConfirmFormWidget(),
                    const SizedBox(height: 12),
                    _descriptionWidget(),
                    const SizedBox(height: 24),
                    _biometricWidget(),
                    Expanded(child: Container()),
                  ],
                ),
              ),
              _bottomWidget(),
            ],
          ),
        ),
      ),
    );
  }
}
