import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/core/constant/enum_constant.dart';
import 'package:toii_social/cubit/auth/register/register_create_wallet/register_create_wallet_cubit.dart';
import 'package:toii_social/cubit/auth/register/register_verify_step/register_verify_step_cubit.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/generated/l10n.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/utils/widget/dismiss_keyboard_widget.dart';
import 'package:toii_social/widget/button/button.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';
import 'package:toii_social/widget/text_field.dart/text_field.dart';

class RegisterCreateNameScreen extends StatefulWidget {
  final RegisterType registerType;
  const RegisterCreateNameScreen({super.key, required this.registerType});

  @override
  State<RegisterCreateNameScreen> createState() =>
      _RegisterCreateNameScreenState();
}

class _RegisterCreateNameScreenState extends State<RegisterCreateNameScreen> {
  final _userNameController = TextEditingController();
  bool _isEnabledButton = false;

  var _errorUserName = '';
  @override
  Widget build(BuildContext context) {
    return DismissKeyboardWidget(child: _bodyWidget());
  }

  Widget _bottomWidget() {
    return BlocConsumer<RegisterCreateWalletCubit, RegisterCreateWalletState>(
      listener: (context, state) async {
        if (state.status.isSuccess) {
          context.go(RouterEnums.inital.routeName);
        } else if (state.status.isFailure) {
          context.showSnackbar(message: state.errorMessage!);
        }
      },
      builder: (context, state) {
        return Container(
          margin: EdgeInsets.only(
            // left: 16,
            // right: 16,
            bottom: MediaQuery.of(context).padding.bottom + 8,
          ),
          child: TSButton.primary(
            title: S.current.register_continue,
            isEnabled: _isEnabledButton,
            isLoading: state.status.isLoading,
            onPressed: () {
              context.read<RegisterVerifyStepCubit>().updateRequestInfo(
                userName: _userNameController.text,
              );
              if (widget.registerType == RegisterType.wallet) {
                context
                    .read<RegisterCreateWalletCubit>()
                    .completeRegistrationWallet(
                      userName: _userNameController.text,
                    );
              } else if (widget.registerType == RegisterType.social) {
                context.read<RegisterVerifyStepCubit>().updateRequestInfo(
                  registerType: "social",
                );
                context.read<RegisterVerifyStepCubit>().isSocial = true;
                context.push(RouterEnums.registerCreatePin.routeName);
              } else {
                context.push(RouterEnums.registerCreatePassword.routeName);
              }
            },
          ),
        );
      },
    );
  }

  Widget _userNameFormWidget() {
    return TTextField(
      autofocus: true,
      labelText: S.current.register_username_field_label,
      hintText: S.current.register_username_field_label,

      onChanged: (value) {
        if (_userNameController.text.isEmpty) {
          _isEnabledButton = false;
          setState(() {
            _errorUserName = S.current.register_create_username_error;
          });
        } else {
          _isEnabledButton = true;
          setState(() {
            _errorUserName = '';
          });
        }
      },

      errorText: _errorUserName.isNotEmpty ? _errorUserName : null,
      textController: _userNameController,
    );
  }

  Widget _bodyWidget() {
    final info = context.read<RegisterVerifyStepCubit>().requestInfo;
    return BlocProvider(
      create: (_) => RegisterCreateWalletCubit(requestInfo: info),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 16),
                    Text(
                      S.current.register_create_username_title,
                      style: headlineSmall.copyColor(themeData.neutral800),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      S.current.register_create_username_subtitle,
                      style: bodyMedium.copyColor(themeData.neutral400),
                    ),

                    const SizedBox(height: 24),
                    _userNameFormWidget(),

                    Expanded(child: Container()),
                  ],
                ),
              ),
              _bottomWidget(),
            ],
          ),
        ),
      ),
    );
  }
}
