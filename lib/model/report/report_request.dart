import 'package:json_annotation/json_annotation.dart';

part 'report_request.g.dart';

@JsonSerializable()
class ReportRequestModel {
  final String description;
  final List<String> reasons;
  @JsonKey(name: 'reported_item_id')
  final String reportedItemId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'reported_item_type')
  final String reportedItemType;

  const ReportRequestModel({
    required this.description,
    required this.reasons,
    required this.reportedItemId,
    required this.reportedItemType,
  });

  factory ReportRequestModel.fromJson(Map<String, dynamic> json) =>
      _$ReportRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$ReportRequestModelToJson(this);
}

@JsonSerializable()
class ReportResponseModel {
  final String id;
  final String description;
  final List<String> reasons;
  @JsonKey(name: 'reported_item_id')
  final String reportedItemId;
  @JsonKey(name: 'reported_item_type')
  final String reportedItemType;
  @Json<PERSON>ey(name: 'created_at')
  final String createdAt;
  final String status;

  const ReportResponseModel({
    required this.id,
    required this.description,
    required this.reasons,
    required this.reportedItemId,
    required this.reportedItemType,
    required this.createdAt,
    required this.status,
  });

  factory ReportResponseModel.fromJson(Map<String, dynamic> json) =>
      _$ReportResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$ReportResponseModelToJson(this);
}
