// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'report_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ReportRequestModel _$ReportRequestModelFromJson(Map<String, dynamic> json) =>
    ReportRequestModel(
      description: json['description'] as String,
      reasons:
          (json['reasons'] as List<dynamic>).map((e) => e as String).toList(),
      reportedItemId: json['reported_item_id'] as String,
      reportedItemType: json['reported_item_type'] as String,
    );

Map<String, dynamic> _$ReportRequestModelToJson(ReportRequestModel instance) =>
    <String, dynamic>{
      'description': instance.description,
      'reasons': instance.reasons,
      'reported_item_id': instance.reportedItemId,
      'reported_item_type': instance.reportedItemType,
    };

ReportResponseModel _$ReportResponseModelFromJson(Map<String, dynamic> json) =>
    ReportResponseModel(
      id: json['id'] as String,
      description: json['description'] as String,
      reasons:
          (json['reasons'] as List<dynamic>).map((e) => e as String).toList(),
      reportedItemId: json['reported_item_id'] as String,
      reportedItemType: json['reported_item_type'] as String,
      createdAt: json['created_at'] as String,
      status: json['status'] as String,
    );

Map<String, dynamic> _$ReportResponseModelToJson(
        ReportResponseModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'description': instance.description,
      'reasons': instance.reasons,
      'reported_item_id': instance.reportedItemId,
      'reported_item_type': instance.reportedItemType,
      'created_at': instance.createdAt,
      'status': instance.status,
    };
