import 'package:hive/hive.dart';
import 'package:web3dart/crypto.dart';
import 'package:web3dart/web3dart.dart';

part 'transaction.g.dart';

@HiveType(typeId: 0)
class Transaction extends HiveObject {
  @HiveField(0)
  final String transactionHash;

  @HiveField(1)
  DateTime timeStamp;

  @HiveField(2)
  String amount;

  factory Transaction.fromTransactionReceipt(
    TransactionReceipt receipt,
    String walletAddress,
  ) {
    return Transaction(
      transactionHash: bytesToHex(receipt.transactionHash, include0x: true),
      timeStamp: DateTime.now(),
      amount: "0", // Placeholder, as amount is not directly available in TransactionReceipt
      // description: 'Transaction from $walletAddress',
      // date: DateTime.now().toIso8601String(),
    );
  }

  Transaction({
    required this.transactionHash,
    required this.timeStamp,
    required this.amount,
  });

  // Map<String, dynamic> toMap() {
  //   return {
  //     'transactionHash': transactionHash,
  //     'timeStamp': timeStamp,
  //     // 'amount': amount,
  //     // 'description': description,
  //     // 'date': date,
  //   };
  // }

  // factory Transaction.fromMap(Map<String, dynamic> map) {
  //   return Transaction(
  //     transactionHash: map['transactionHash'] ?? '',
  //     timeStamp: DateTime.parse(
  //       map['timeStamp'] ?? DateTime.now().toIso8601String(),
  //     ),
  //     // type: map['type'],
  //     // amount: map['amount'],
  //     // description: map['description'],
  //     // date: map['date'],
  //   );
  // }
}
