import 'package:json_annotation/json_annotation.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/model/post/reaction_model.dart';
import 'package:toii_social/model/user/user_model.dart';
import 'package:toii_social/utils/date_format/date_format.dart';

part 'comment_model.g.dart';

@JsonSerializable(explicitToJson: true)
class CommentListModel {
  const CommentListModel({this.comments = const [], required this.total});

  final List<CommentItemModel> comments;
  @JsonKey(defaultValue: 0)
  final int total;

  factory CommentListModel.fromJson(Map<String, dynamic> json) =>
      _$CommentListModelFromJson(json);

  Map<String, dynamic> toJson() => _$CommentListModelToJson(this);
}

@JsonSerializable()
class CommentItemModel {
  const CommentItemModel({
    required this.id,
    this.postId,
    this.user,
    required this.content,
    required this.likes,
    this.createdAt,
    this.updatedAt,
    this.reactions,
  });

  @JsonKey(defaultValue: "")
  final String id;

  @JsonKey(name: 'post_id')
  final String? postId;

  final UserModel? user;
  @JsonKey(defaultValue: "")
  final String content;
  @JsonKey(defaultValue: 0)
  final int likes;

  @JsonKey(name: 'created_at')
  final String? createdAt;

  @JsonKey(name: 'updated_at')
  final String? updatedAt;

  final ReactionGroupModel? reactions;

  factory CommentItemModel.fromJson(Map<String, dynamic> json) =>
      _$CommentItemModelFromJson(json);

  Map<String, dynamic> toJson() => _$CommentItemModelToJson(this);

  int get actionlikes {
    if (reactions == null) return 0;
    // todo sửa enum action sau
    return reactions!.reactions
            ?.where((reaction) => reaction.type?.toLowerCase() == 'love')
            .firstOrNull
            ?.count ??
        0;
  }

  bool get isLiked {
    if (reactions == null) return false;
    return reactions!.userReactions?.contains('love') ?? false;
    // todo sửa enum action sau
  }

  String get getViewLike {
    if (actionlikes == 0) return "Like";
    if (actionlikes >= 1000)
      return "${(actionlikes / 1000).toStringAsFixed(1)}K";
    return actionlikes.toString();
  }
}

extension CommentItemModelExtension on CommentItemModel {
  String get updatedAtTime {
    if (updatedAt == null || updatedAt!.isEmpty) {
      return "";
    }
    final parsedDate = DateTime.parse(updatedAt!).toLocal();
    return formatDate(parsedDate);
  }
}

@JsonSerializable()
class CreateCommentRequestModel {
  const CreateCommentRequestModel({
    required this.content,
    this.mediaKeys = const [],
  });
  final String content;

  @JsonKey(name: 'media_keys')
  final List<String> mediaKeys;

  factory CreateCommentRequestModel.fromJson(Map<String, dynamic> json) =>
      _$CreateCommentRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$CreateCommentRequestModelToJson(this);
}
