// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'post_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PostResponseDataModel _$PostResponseDataModelFromJson(
        Map<String, dynamic> json) =>
    PostResponseDataModel(
      posts: (json['posts'] as List<dynamic>)
          .map((e) => PostModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: (json['total'] as num).toInt(),
    );

Map<String, dynamic> _$PostResponseDataModelToJson(
        PostResponseDataModel instance) =>
    <String, dynamic>{
      'posts': instance.posts,
      'total': instance.total,
    };

PostModel _$PostModelFromJson(Map<String, dynamic> json) => PostModel(
      id: json['id'] as String,
      user: json['user'] == null
          ? null
          : UserModel.fromJson(json['user'] as Map<String, dynamic>),
      content: json['content'] as String? ?? '',
      mediaKeys: (json['media_keys'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      mediaUrls: (json['media_urls'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      reposts: (json['reposts'] as num?)?.toInt() ?? 0,
      comments: (json['comments'] as num?)?.toInt() ?? 0,
      reactions: json['reactions'] == null
          ? null
          : ReactionGroupModel.fromJson(
              json['reactions'] as Map<String, dynamic>),
      privacy: json['privacy'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );

Map<String, dynamic> _$PostModelToJson(PostModel instance) => <String, dynamic>{
      'id': instance.id,
      'user': instance.user,
      'content': instance.content,
      'media_keys': instance.mediaKeys,
      'media_urls': instance.mediaUrls,
      'reposts': instance.reposts,
      'comments': instance.comments,
      'reactions': instance.reactions,
      'privacy': instance.privacy,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };

CreatePostRequestModel _$CreatePostRequestModelFromJson(
        Map<String, dynamic> json) =>
    CreatePostRequestModel(
      content: json['content'] as String?,
      privacy: json['privacy'] as String,
      mediaKeys: (json['media_keys'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$CreatePostRequestModelToJson(
        CreatePostRequestModel instance) =>
    <String, dynamic>{
      'content': instance.content,
      'media_keys': instance.mediaKeys,
      'privacy': instance.privacy,
    };
