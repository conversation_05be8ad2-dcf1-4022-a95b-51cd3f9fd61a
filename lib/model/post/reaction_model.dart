import 'package:json_annotation/json_annotation.dart';

part 'reaction_model.g.dart';

@JsonSerializable()
class ReactionGroupModel {
  @JsonKey(name: 'total_reactions')
  final int? totalReactions;
  final List<ReactionModel>? reactions;
  @Json<PERSON>ey(name: 'user_reaction')
  final String? userReaction;
  @Json<PERSON>ey(name: 'user_reactions')
  final List<String>? userReactions;

  const ReactionGroupModel({
    this.totalReactions,
    this.reactions,
    this.userReaction,
    this.userReactions,
  });

  factory ReactionGroupModel.fromJson(Map<String, dynamic> json) =>
      _$ReactionGroupModelFromJson(json);
  Map<String, dynamic> toJson() => _$ReactionGroupModelToJson(this);

  ReactionGroupModel copyWith({
    int? totalReactions,
    List<ReactionModel>? reactions,
    String? userReaction,
    List<String>? userReactions,
  }) {
    return ReactionGroupModel(
      totalReactions: totalReactions ?? this.totalReactions,
      reactions: reactions ?? this.reactions,
      userReaction: userReaction ?? this.userReaction,
      userReactions: userReactions ?? this.userReactions,
    );
  }
}

@JsonSerializable()
class ReactionModel {
  final String? type;
  final String? emoji;
  final String? name;
  final int? count;

  const ReactionModel({this.type, this.emoji, this.name, this.count});

  factory ReactionModel.fromJson(Map<String, dynamic> json) =>
      _$ReactionModelFromJson(json);
  Map<String, dynamic> toJson() => _$ReactionModelToJson(this);

  ReactionModel copyWith({
    String? type,
    String? emoji,
    String? name,
    int? count,
  }) {
    return ReactionModel(
      type: type ?? this.type,
      emoji: emoji ?? this.emoji,
      name: name ?? this.name,
      count: count ?? this.count,
    );
  }
}
