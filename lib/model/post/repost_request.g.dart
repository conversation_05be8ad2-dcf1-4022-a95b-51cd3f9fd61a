// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'repost_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RepostRequest _$RepostRequestFromJson(Map<String, dynamic> json) =>
    RepostRequest(
      originalPostId: json['original_post_id'] as String,
      repostContent: json['repost_content'] as String?,
      mediaKeys: (json['media_keys'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      privacy: json['privacy'] as String? ?? 'public',
      allowedUserIds: (json['allowed_user_ids'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$RepostRequestToJson(RepostRequest instance) =>
    <String, dynamic>{
      'original_post_id': instance.originalPostId,
      'repost_content': instance.repostContent,
      'media_keys': instance.mediaKeys,
      'privacy': instance.privacy,
      'allowed_user_ids': instance.allowedUserIds,
    };
