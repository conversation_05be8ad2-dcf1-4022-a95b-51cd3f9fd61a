import 'package:json_annotation/json_annotation.dart';

part 'repost_request.g.dart';

@JsonSerializable()
class RepostRequest {
  const RepostRequest({
    required this.originalPostId,
    this.repostContent,
    this.mediaKeys,
    this.privacy = 'public',
    this.allowedUserIds,
  });

  @Json<PERSON><PERSON>(name: 'original_post_id')
  final String originalPostId;

  @Json<PERSON>ey(name: 'repost_content')
  final String? repostContent;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'media_keys')
  final List<String>? mediaKeys;

  final String privacy;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'allowed_user_ids')
  final List<String>? allowedUserIds;

  factory RepostRequest.fromJson(Map<String, dynamic> json) =>
      _$RepostRequestFromJson(json);

  Map<String, dynamic> toJson() => _$RepostRequestToJson(this);
}
