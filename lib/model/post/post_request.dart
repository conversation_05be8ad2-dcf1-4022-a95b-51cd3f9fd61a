import 'package:json_annotation/json_annotation.dart';

part 'post_request.g.dart';

@JsonSerializable()
class PostRequest {
  final List<String>? allowedUserIds;
  final String? content;
  @JsonKey(name: 'media_keys')
  final List<String>? mediaKeys;
  final String? privacy;

  PostRequest({
    this.allowedUserIds,
      this.content,
      this.mediaKeys,
      this.privacy,
  });

  factory PostRequest.fromJson(Map<String, dynamic> json) =>
      _$PostRequestFromJson(json);

  Map<String, dynamic> toJson() => _$PostRequestToJson(this);
}
