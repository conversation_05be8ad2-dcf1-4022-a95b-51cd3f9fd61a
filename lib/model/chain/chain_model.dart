import 'package:json_annotation/json_annotation.dart';

import 'token_model.dart';

// @JsonSerializable()
// class ChainModel {
//   String? name;
//   String? rpcUrl;
//   String? rpc;
//   String chainId;
//   String? symbol;
//   double balance = 0;
//   String? explorer;
//   bool? isDefault;
//   String? iconUrl;

//   List<TokenModel> tokens = [];

//   ChainModel(
//       {this.name,
//       this.rpcUrl,
//       required this.chainId,
//       this.symbol,
//       this.balance = 0,
//       this.tokens = const [],
//       this.iconUrl,
//       this.isDefault});
//   factory ChainModel.fromJson(Map<String, dynamic> json) =>
//       _$ChainModelFromJson(json);

//   Map<String, dynamic> toJson() => _$ChainModelToJson(this);

//   ChainModel copyWith({
//     String? name,
//     String? rpcUrl,
//     String? chainId,
//     String? symbol,
//     double? balance,
//     List<TokenModel>? tokens,
//     String? iconUrl,
//   }) {
//     return ChainModel(
//       name: name ?? this.name,
//       rpcUrl: rpcUrl ?? this.rpcUrl,
//       chainId: chainId ?? this.chainId,
//       symbol: symbol ?? this.symbol,
//       balance: balance ?? this.balance,
//       tokens: tokens ?? this.tokens,
//       iconUrl: iconUrl ?? this.iconUrl,
//     );
//   }
// }
class ChainModel {
  final String name;
  final int chainId;
  final String chain;
  final String shortName;
  final List<String> rpc;
  final String? explorerUrl;
  final String? nativeSymbol;
  final int? nativeDecimals;
  final String? logoURI;

  ChainModel({
    required this.chain,
    required this.name,
    required this.chainId,
    required this.shortName,
    required this.rpc,
    this.explorerUrl,
    this.nativeSymbol,
    this.nativeDecimals,
    this.logoURI,
  });

  factory ChainModel.fromJson(Map<String, dynamic> json) {
    final explorers = json['explorers'] as List<dynamic>?;
    final explorerUrl =
        explorers != null && explorers.isNotEmpty
            ? explorers.first['url'] as String?
            : null;

    final nativeCurrency = json['nativeCurrency'] as Map<String, dynamic>?;

    return ChainModel(
      chain: json['chain'] ?? '',
      name: json['name'] ?? '',
      chainId: json['chainId'] ?? 0,
      shortName: json['shortName'] ?? '',
      rpc: List<String>.from(json['rpc'] ?? []),
      explorerUrl: explorerUrl,
      nativeSymbol: nativeCurrency?['symbol'],
      nativeDecimals: nativeCurrency?['decimals'],
      logoURI: json['logoURI'], // Some custom datasets include this
    );
  }
}
