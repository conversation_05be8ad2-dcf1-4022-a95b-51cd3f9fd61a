import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:toii_social/model/chain/cryptocurrency.dart';


part 'listings_model.g.dart';

@JsonSerializable()
class ListingsModel extends Equatable {
  final StatusModel? status;
  @JsonKey(name: 'data')
  final List<CryptocurrencyItem> token;

  const ListingsModel({this.status, this.token = const []});

  factory ListingsModel.fromJson(Map<String, dynamic> json) {
    return _$ListingsModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$ListingsModelToJson(this);

  @override
  bool get stringify => true;

  @override
  List<Object?> get props => [status, token];
}

@JsonSerializable()
class StatusModel extends Equatable {
  final DateTime? timestamp;
  @JsonKey(name: 'error_code')
  final int? errorCode;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'error_message')
  final dynamic errorMessage;
  final int? elapsed;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'credit_count')
  final int? creditCount;
  final dynamic notice;
  @JsonKey(name: 'total_count')
  final int? totalCount;

  const StatusModel({
    this.timestamp,
    this.errorCode,
    this.errorMessage,
    this.elapsed,
    this.creditCount,
    this.notice,
    this.totalCount,
  });

  factory StatusModel.fromJson(Map<String, dynamic> json) {
    return _$StatusModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$StatusModelToJson(this);

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [
      timestamp,
      errorCode,
      errorMessage,
      elapsed,
      creditCount,
      notice,
      totalCount,
    ];
  }
}
