import 'package:json_annotation/json_annotation.dart';

part 'token_model.g.dart';

@JsonSerializable()
class TokenModel {
  String? name;
  String? contract;
  String? symbol;
  String? decimal;

  TokenModel({this.name, this.contract, this.symbol, this.decimal});

  factory TokenModel.fromJson(Map<String, dynamic> json) =>
      _$TokenModelFromJson(json);

  Map<String, dynamic> toJson() => _$TokenModelToJson(this);

  TokenModel copyWith({
    String? name,
    String? contract,
    String? symbol,
    String? decimal,
  }) {
    return TokenModel(
      name: name ?? this.name,
      contract: contract ?? this.contract,
      symbol: symbol ?? this.symbol,
      decimal: decimal ?? this.decimal,
    );
  }
}
