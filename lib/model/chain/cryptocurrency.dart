import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'cryptocurrency.g.dart';

@JsonSerializable()
class CryptocurrencyItem extends Equatable {
  int? id;
  final String? name;
  final String? symbol;
  final String? slug;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'num_market_pairs')
  final int? numMarketPairs;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'date_added')
  final DateTime? dateAdded;
  final List<String>? tags;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'max_supply')
  final int? maxSupply;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'circulating_supply')
  final double? circulatingSupply;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'total_supply')
  final double? totalSupply;
  final PlatformModel? platform;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'cmc_rank')
  final int? cmcRank;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'self_reported_circulating_supply')
  final dynamic selfReportedCirculatingSupply;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'self_reported_market_cap')
  final dynamic selfReportedMarketCap;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'tvl_ratio')
  final dynamic tvlRatio;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_updated')
  final DateTime? lastUpdated;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'quote')
  final QuoteModel? quoteModel;

  double balance;

  CryptocurrencyItem({
    this.id,
    this.name,
    this.symbol,
    this.slug,
    this.numMarketPairs,
    this.dateAdded,
    this.tags,
    this.maxSupply,
    this.circulatingSupply,
    this.totalSupply,
    this.platform,
    this.cmcRank,
    this.selfReportedCirculatingSupply,
    this.selfReportedMarketCap,
    this.tvlRatio,
    this.lastUpdated,
    this.quoteModel,
    this.balance = 0.0,
  });

  factory CryptocurrencyItem.fromJson(Map<String, dynamic> json) =>
      _$CryptocurrencyItemFromJson(json);

  Map<String, dynamic> toJson() => _$CryptocurrencyItemToJson(this);

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [
      id,
      name,
      symbol,
      slug,
      numMarketPairs,
      dateAdded,
      tags,
      maxSupply,
      circulatingSupply,
      totalSupply,
      platform,
      cmcRank,
      selfReportedCirculatingSupply,
      selfReportedMarketCap,
      tvlRatio,
      lastUpdated,
      quoteModel,
    ];
  }
}

@JsonSerializable()
class PlatformModel extends Equatable {
  @JsonKey(name: 'token_address')
  final String? tokenAdress;

  const PlatformModel({this.tokenAdress});

  factory PlatformModel.fromJson(Map<String, dynamic> json) =>
      _$PlatformModelFromJson(json);

  Map<String, dynamic> toJson() => _$PlatformModelToJson(this);

  @override
  bool get stringify => true;

  @override
  List<Object?> get props => [tokenAdress];
}

@JsonSerializable()
class UsdModel extends Equatable {
  double? price;
  @JsonKey(name: 'volume_24h')
  final double? volume24h;
  @JsonKey(name: 'volume_change_24h')
  final double? volumeChange24h;
  @JsonKey(name: 'percent_change_1h')
  final double? percentChange1h;
  @JsonKey(name: 'percent_change_24h')
  final double? percentChange24h;
  @JsonKey(name: 'percent_change_7d')
  final double? percentChange7d;
  @JsonKey(name: 'percent_change_30d')
  final double? percentChange30d;
  @JsonKey(name: 'percent_change_60d')
  final double? percentChange60d;
  @JsonKey(name: 'percent_change_90d')
  final double? percentChange90d;
  @JsonKey(name: 'market_cap')
  final double? marketCap;
  @JsonKey(name: 'market_cap_dominance')
  final double? marketCapDominance;
  @JsonKey(name: 'fully_diluted_market_cap')
  final double? fullyDilutedMarketCap;
  final dynamic tvl;
  @JsonKey(name: 'last_updated')
  final DateTime? lastUpdated;

  UsdModel({
    this.price,
    this.volume24h,
    this.volumeChange24h,
    this.percentChange1h,
    this.percentChange24h,
    this.percentChange7d,
    this.percentChange30d,
    this.percentChange60d,
    this.percentChange90d,
    this.marketCap,
    this.marketCapDominance,
    this.fullyDilutedMarketCap,
    this.tvl,
    this.lastUpdated,
  });

  factory UsdModel.fromJson(Map<String, dynamic> json) =>
      _$UsdModelFromJson(json);

  Map<String, dynamic> toJson() => _$UsdModelToJson(this);

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [
      price,
      volume24h,
      volumeChange24h,
      percentChange1h,
      percentChange24h,
      percentChange7d,
      percentChange30d,
      percentChange60d,
      percentChange90d,
      marketCap,
      marketCapDominance,
      fullyDilutedMarketCap,
      tvl,
      lastUpdated,
    ];
  }
}

@JsonSerializable()
class QuoteModel extends Equatable {
  @JsonKey(name: 'USD')
  final UsdModel? usd;

  const QuoteModel({this.usd});

  factory QuoteModel.fromJson(Map<String, dynamic> json) =>
      _$QuoteModelFromJson(json);

  Map<String, dynamic> toJson() => _$QuoteModelToJson(this);

  @override
  bool get stringify => true;

  @override
  List<Object?> get props => [usd];
}

