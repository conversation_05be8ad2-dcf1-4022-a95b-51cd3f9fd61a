// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'listings_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ListingsModel _$ListingsModelFromJson(Map<String, dynamic> json) =>
    ListingsModel(
      status: json['status'] == null
          ? null
          : StatusModel.fromJson(json['status'] as Map<String, dynamic>),
      token: (json['data'] as List<dynamic>?)
              ?.map(
                  (e) => CryptocurrencyItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$ListingsModelToJson(ListingsModel instance) =>
    <String, dynamic>{
      'status': instance.status,
      'data': instance.token,
    };

StatusModel _$StatusModelFromJson(Map<String, dynamic> json) => StatusModel(
      timestamp: json['timestamp'] == null
          ? null
          : DateTime.parse(json['timestamp'] as String),
      errorCode: (json['error_code'] as num?)?.toInt(),
      errorMessage: json['error_message'],
      elapsed: (json['elapsed'] as num?)?.toInt(),
      creditCount: (json['credit_count'] as num?)?.toInt(),
      notice: json['notice'],
      totalCount: (json['total_count'] as num?)?.toInt(),
    );

Map<String, dynamic> _$StatusModelToJson(StatusModel instance) =>
    <String, dynamic>{
      'timestamp': instance.timestamp?.toIso8601String(),
      'error_code': instance.errorCode,
      'error_message': instance.errorMessage,
      'elapsed': instance.elapsed,
      'credit_count': instance.creditCount,
      'notice': instance.notice,
      'total_count': instance.totalCount,
    };
