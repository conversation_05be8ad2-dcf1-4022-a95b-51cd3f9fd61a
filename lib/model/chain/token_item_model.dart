import 'package:json_annotation/json_annotation.dart';

part 'token_item_model.g.dart';

@JsonSerializable()
class TokenItemModel {
  int? id;

  final String? symbol;

  final String? address;

  final String name;

  final String? iconId;

  final String? iconUrl;

  double? balance;

  final bool isHidden;

  final int chainId;

  final bool isNative;

  double? price;

  int idLogo;

  final String chainName;

  TokenItemModel({
    this.id,
    this.symbol,
    this.address,
    required this.name,
    this.iconId,
    this.iconUrl,
    this.balance,
    this.isHidden = false,
    required this.chainId,
    this.isNative = false,
    this.price,
    this.idLogo = -1,
    this.chainName = "",
  });

  factory TokenItemModel.fromJson(Map<String, dynamic> json) =>
      _$TokenItemModelFromJson(json);

  Map<String, dynamic> toJson() => _$TokenItemModelToJson(this);
}
