// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'token_item_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TokenItemModel _$TokenItemModelFromJson(Map<String, dynamic> json) =>
    TokenItemModel(
      id: (json['id'] as num?)?.toInt(),
      symbol: json['symbol'] as String?,
      address: json['address'] as String?,
      name: json['name'] as String,
      iconId: json['iconId'] as String?,
      iconUrl: json['iconUrl'] as String?,
      balance: (json['balance'] as num?)?.toDouble(),
      isHidden: json['isHidden'] as bool? ?? false,
      chainId: (json['chainId'] as num).toInt(),
      isNative: json['isNative'] as bool? ?? false,
      price: (json['price'] as num?)?.toDouble(),
      idLogo: (json['idLogo'] as num?)?.toInt() ?? -1,
      chainName: json['chainName'] as String? ?? "",
    );

Map<String, dynamic> _$TokenItemModelToJson(TokenItemModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'symbol': instance.symbol,
      'address': instance.address,
      'name': instance.name,
      'iconId': instance.iconId,
      'iconUrl': instance.iconUrl,
      'balance': instance.balance,
      'isHidden': instance.isHidden,
      'chainId': instance.chainId,
      'isNative': instance.isNative,
      'price': instance.price,
      'idLogo': instance.idLogo,
      'chainName': instance.chainName,
    };
