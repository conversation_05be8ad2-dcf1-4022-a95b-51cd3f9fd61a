import 'package:json_annotation/json_annotation.dart';

part 'media_model.g.dart';

@JsonSerializable()
class MediaUploadResponseModel {
  const MediaUploadResponseModel({
    this.id,
    this.userId,
    this.type,
    this.fileName,
    this.bucket,
    this.key,
    this.s3Url,
    this.cdnUrl,
    this.size,
    this.contentType,
    this.createdAt,
    this.updatedAt,
  });

  final String? id;
  @JsonKey(name: 'user_id')
  final String? userId;
  final String? type;
  @JsonKey(name: 'file_name')
  final String? fileName;
  final String? bucket;
  final String? key;
  @<PERSON><PERSON><PERSON><PERSON>(name: 's3_url')
  final String? s3Url;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'cdn_url')
  final String? cdnUrl;
  final int? size;
  @JsonKey(name: 'content_type')
  final String? contentType;
  @Json<PERSON>ey(name: 'created_at')
  final String? createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final String? updatedAt;

  factory MediaUploadResponseModel.fromJson(Map<String, dynamic> json) =>
      _$MediaUploadResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$MediaUploadResponseModelToJson(this);
}

@JsonSerializable()
class MediaUploadRequestModel {
  const MediaUploadRequestModel({required this.type});

  final String type;

  factory MediaUploadRequestModel.fromJson(Map<String, dynamic> json) =>
      _$MediaUploadRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$MediaUploadRequestModelToJson(this);
}
