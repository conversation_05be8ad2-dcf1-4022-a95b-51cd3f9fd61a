import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

@JsonSerializable()
class UserModel {
  final String id;
  final String username;
  final String? phoneNumber;
  final String? email;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'first_name')
  final String? firstName;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_name')
  final String? lastName;

  @J<PERSON><PERSON><PERSON>(name: 'full_name')
  final String? fullName;

  final String? avatar;
  final String? address;

 @<PERSON><PERSON><PERSON><PERSON>(name: 'wallet_address')
  final String? walletAddress;

  @<PERSON>son<PERSON><PERSON>(name: 'more_info')
  final Map<String, dynamic>? moreInfo;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_activity')
  final DateTime lastActivity;

  final String? role;
  final String? status;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final DateTime? createdAt;

  @Json<PERSON>ey(name: 'updated_at')
  final DateTime? updatedAt;

  @J<PERSON><PERSON>ey(name: 'deleted_at')
  final DateTime? deletedAt;

  UserModel({
    required this.id,
    required this.username,
    this.phoneNumber,
    this.email,
    this.firstName,
    required this.lastName,
    required this.fullName,
    required this.avatar,
    required this.address,
    required this.moreInfo,
    required this.lastActivity,
    required this.role,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    this.walletAddress
  });

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserModelToJson(this);
}
