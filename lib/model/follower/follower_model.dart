import 'package:json_annotation/json_annotation.dart';
import 'package:toii_social/model/user/user_model.dart';

part 'follower_model.g.dart';

@JsonSerializable()
class FollowerListModel {
  const FollowerListModel({
    this.followers = const [],
    required this.total,
  });

  final List<UserModel> followers;
  @JsonKey(defaultValue: 0)
  final int total;

  factory FollowerListModel.fromJson(Map<String, dynamic> json) =>
      _$FollowerListModelFromJson(json);

  Map<String, dynamic> toJson() => _$FollowerListModelToJson(this);
}

@JsonSerializable()
class FollowingListModel {
  const FollowingListModel({
    this.following = const [],
    required this.total,
  });

  final List<UserModel> following;
  @JsonKey(defaultValue: 0)
  final int total;

  factory FollowingListModel.fromJson(Map<String, dynamic> json) =>
      _$FollowingListModelFromJson(json);

  Map<String, dynamic> toJson() => _$FollowingListModelToJson(this);
}
