// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'follower_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FollowerListModel _$FollowerListModelFromJson(Map<String, dynamic> json) =>
    FollowerListModel(
      followers: (json['followers'] as List<dynamic>?)
              ?.map((e) => UserModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      total: (json['total'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$FollowerListModelToJson(FollowerListModel instance) =>
    <String, dynamic>{
      'followers': instance.followers,
      'total': instance.total,
    };

FollowingListModel _$FollowingListModelFromJson(Map<String, dynamic> json) =>
    FollowingListModel(
      following: (json['following'] as List<dynamic>?)
              ?.map((e) => UserModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      total: (json['total'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$FollowingListModelToJson(FollowingListModel instance) =>
    <String, dynamic>{
      'following': instance.following,
      'total': instance.total,
    };
