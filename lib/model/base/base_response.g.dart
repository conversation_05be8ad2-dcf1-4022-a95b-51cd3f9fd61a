// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BaseResponse<T> _$BaseResponseFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) =>
    BaseResponse<T>(
      data: fromJsonT(json['data']),
    );

Map<String, dynamic> _$BaseResponseToJson<T>(
  BaseResponse<T> instance,
  Object? Function(T value) toJsonT,
) =>
    <String, dynamic>{
      'data': toJsonT(instance.data),
    };

BaseListData<T> _$BaseListDataFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) =>
    BaseListData<T>(
      items: (json['items'] as List<dynamic>).map(fromJsonT).toList(),
      totalItems: (json['total_items'] as num?)?.toInt(),
      totalPages: (json['total_pages'] as num?)?.toInt(),
      currentPage: (json['current_page'] as num?)?.toInt(),
      itemsPerPage: (json['items_per_page'] as num?)?.toInt(),
      endCursor: json['end_cursor'] as String?,
      hasNext: json['has_next'] as bool?,
      totalCount: (json['total_count'] as num?)?.toInt(),
    );

Map<String, dynamic> _$BaseListDataToJson<T>(
  BaseListData<T> instance,
  Object? Function(T value) toJsonT,
) =>
    <String, dynamic>{
      'items': instance.items.map(toJsonT).toList(),
      'end_cursor': instance.endCursor,
      'has_next': instance.hasNext,
      'total_count': instance.totalCount,
      'total_items': instance.totalItems,
      'total_pages': instance.totalPages,
      'current_page': instance.currentPage,
      'items_per_page': instance.itemsPerPage,
    };

BaseErrorResponse _$BaseErrorResponseFromJson(Map<String, dynamic> json) =>
    BaseErrorResponse(
      message: json['message'] == null
          ? null
          : BaseErrorMessageResponse.fromJson(
              json['message'] as Map<String, dynamic>),
      code: (json['code'] as num?)?.toInt(),
    );

Map<String, dynamic> _$BaseErrorResponseToJson(BaseErrorResponse instance) =>
    <String, dynamic>{
      'message': instance.message,
      'code': instance.code,
    };

BaseErrorMessageResponse _$BaseErrorMessageResponseFromJson(
        Map<String, dynamic> json) =>
    BaseErrorMessageResponse(
      message: json['message'] as String? ?? '',
    );

Map<String, dynamic> _$BaseErrorMessageResponseToJson(
        BaseErrorMessageResponse instance) =>
    <String, dynamic>{
      'message': instance.message,
    };
