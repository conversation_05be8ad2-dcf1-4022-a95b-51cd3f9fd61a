import 'package:json_annotation/json_annotation.dart';

part 'base_response.g.dart';

@JsonSerializable(genericArgumentFactories: true)
class BaseResponse<T> {
  factory BaseResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$BaseResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object? Function(T value) toJsonR) =>
      _$BaseResponseToJson(this, toJsonR);

  @JsonKey(name: 'data')
  final T data;

  const BaseResponse({
    required this.data,
  });
}

@JsonSerializable(genericArgumentFactories: true)
class BaseListData<T> {
  factory BaseListData.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
     _$BaseListDataFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object? Function(T value) toJsonR) =>
      _$BaseListDataToJson(this, toJsonR);

  @JsonKey(name: 'items')
  final List<T> items;

  @JsonKey(name: 'end_cursor')
  final String? endCursor;

  @JsonKey(name: 'has_next')
  final bool? hasNext;

  @JsonKey(name: 'total_count')
  final int? totalCount;

  @JsonKey(name: 'total_items')
  final int? totalItems;

  @JsonKey(name: 'total_pages')
  final int? totalPages;

  @JsonKey(name: 'current_page')
  final int? currentPage;

  @JsonKey(name: 'items_per_page')
  final int? itemsPerPage;

  const BaseListData({
    required this.items,
    this.totalItems,
    this.totalPages,
    this.currentPage,
    this.itemsPerPage,
    this.endCursor,
    this.hasNext,
    this.totalCount,
  });
}

@JsonSerializable()
class BaseErrorResponse {
  factory BaseErrorResponse.fromJson(Map<String, dynamic> json) =>
      _$BaseErrorResponseFromJson(json);

  Map<String, dynamic> toJson() => _$BaseErrorResponseToJson(this);

  @JsonKey(name: 'message')
  final BaseErrorMessageResponse? message;

  @JsonKey(name: 'code')
  final int? code;

  const BaseErrorResponse({
    this.message,
    this.code,
  });
}

@JsonSerializable()
class BaseErrorMessageResponse {
  factory BaseErrorMessageResponse.fromJson(Map<String, dynamic> json) =>
      _$BaseErrorMessageResponseFromJson(json);

  Map<String, dynamic> toJson() => _$BaseErrorMessageResponseToJson(this);

  @JsonKey(name: 'message', defaultValue: '')
  final String message;

  const BaseErrorMessageResponse({
    required this.message,
  });
}
