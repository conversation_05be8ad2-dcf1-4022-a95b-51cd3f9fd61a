// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class TransactionModelAdapter extends TypeAdapter<TransactionModel> {
  @override
  final int typeId = 1;

  @override
  TransactionModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return TransactionModel(
      hash: fields[1] as String,
      timeStamp: fields[0] as DateTime?,
      status: fields[2] as TransactionStatus,
      type: fields[4] as TransactionType,
      value: fields[5] as String?,
      action: fields[3] as TransactionActions?,
      from: fields[6] as String?,
      to: fields[7] as String?,
      feePerGas: fields[8] as double?,
      data: fields[11] as String?,
      gasLimit: fields[10] as int?,
      nonce: fields[12] as int?,
      maxPriorityFee: fields[9] as BigInt?,
      transferType: fields[14] as TransferType?,
      amount: fields[13] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, TransactionModel obj) {
    writer
      ..writeByte(15)
      ..writeByte(0)
      ..write(obj.timeStamp)
      ..writeByte(1)
      ..write(obj.hash)
      ..writeByte(2)
      ..write(obj.status)
      ..writeByte(3)
      ..write(obj.action)
      ..writeByte(4)
      ..write(obj.type)
      ..writeByte(5)
      ..write(obj.value)
      ..writeByte(6)
      ..write(obj.from)
      ..writeByte(7)
      ..write(obj.to)
      ..writeByte(8)
      ..write(obj.feePerGas)
      ..writeByte(9)
      ..write(obj.maxPriorityFee)
      ..writeByte(10)
      ..write(obj.gasLimit)
      ..writeByte(11)
      ..write(obj.data)
      ..writeByte(12)
      ..write(obj.nonce)
      ..writeByte(14)
      ..write(obj.transferType)
      ..writeByte(13)
      ..write(obj.amount);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TransactionModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class TransactionTypeAdapter extends TypeAdapter<TransactionType> {
  @override
  final int typeId = 4;

  @override
  TransactionType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return TransactionType.sent;
      case 1:
        return TransactionType.received;
      case 2:
        return TransactionType.contractCall;
      case 3:
        return TransactionType.all;
      case 4:
        return TransactionType.unknown;
      default:
        return TransactionType.sent;
    }
  }

  @override
  void write(BinaryWriter writer, TransactionType obj) {
    switch (obj) {
      case TransactionType.sent:
        writer.writeByte(0);
        break;
      case TransactionType.received:
        writer.writeByte(1);
        break;
      case TransactionType.contractCall:
        writer.writeByte(2);
        break;
      case TransactionType.all:
        writer.writeByte(3);
        break;
      case TransactionType.unknown:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TransactionTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class TransferTypeAdapter extends TypeAdapter<TransferType> {
  @override
  final int typeId = 5;

  @override
  TransferType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return TransferType.coin;
      case 1:
        return TransferType.erc20;
      case 2:
        return TransferType.erc1155;
      case 3:
        return TransferType.erc721;
      case 4:
        return TransferType.none;
      default:
        return TransferType.coin;
    }
  }

  @override
  void write(BinaryWriter writer, TransferType obj) {
    switch (obj) {
      case TransferType.coin:
        writer.writeByte(0);
        break;
      case TransferType.erc20:
        writer.writeByte(1);
        break;
      case TransferType.erc1155:
        writer.writeByte(2);
        break;
      case TransferType.erc721:
        writer.writeByte(3);
        break;
      case TransferType.none:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TransferTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class TransactionStatusAdapter extends TypeAdapter<TransactionStatus> {
  @override
  final int typeId = 3;

  @override
  TransactionStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return TransactionStatus.done;
      case 1:
        return TransactionStatus.pending;
      case 2:
        return TransactionStatus.failed;
      default:
        return TransactionStatus.done;
    }
  }

  @override
  void write(BinaryWriter writer, TransactionStatus obj) {
    switch (obj) {
      case TransactionStatus.done:
        writer.writeByte(0);
        break;
      case TransactionStatus.pending:
        writer.writeByte(1);
        break;
      case TransactionStatus.failed:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TransactionStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class TransactionActionsAdapter extends TypeAdapter<TransactionActions> {
  @override
  final int typeId = 2;

  @override
  TransactionActions read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return TransactionActions.cancel;
      case 1:
        return TransactionActions.speedUp;
      case 2:
        return TransactionActions.cancelSpeedUp;
      case 3:
        return TransactionActions.speedUpCancel;
      default:
        return TransactionActions.cancel;
    }
  }

  @override
  void write(BinaryWriter writer, TransactionActions obj) {
    switch (obj) {
      case TransactionActions.cancel:
        writer.writeByte(0);
        break;
      case TransactionActions.speedUp:
        writer.writeByte(1);
        break;
      case TransactionActions.cancelSpeedUp:
        writer.writeByte(2);
        break;
      case TransactionActions.speedUpCancel:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TransactionActionsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
