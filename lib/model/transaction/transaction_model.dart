import 'dart:convert';

import 'package:hive/hive.dart';
import 'package:toii_social/model/chain/token_item_model.dart';
import 'package:web3dart/crypto.dart';
import 'package:web3dart/web3dart.dart';

part 'transaction_model.g.dart';

@HiveType(typeId: 4) enum TransactionType { @HiveField(0) sent, @HiveField(1) received, @HiveField(2) contractCall, @HiveField(3) all, @HiveField(4) unknown }

@HiveType(typeId: 5) enum TransferType { @HiveField(0) coin, @HiveField(1) erc20, @HiveField(2) erc1155, @HiveField(3) erc721, @HiveField(4) none }

@HiveType(typeId: 3)
enum TransactionStatus { @HiveField(0) done, @HiveField(1) pending, @HiveField(2) failed }

extension TransactionStatusDisplay on TransactionStatus {
  String get name {
    switch (this) {
      case TransactionStatus.done:
        return "Complete";
      case TransactionStatus.pending:
        return "Pending";
      case TransactionStatus.failed:
        return "failed";
    }
  }

  // Color get color {
  //   switch (this) {
  //     case TransactionStatus.done:
  //       return green800;
  //     case TransactionStatus.pending:
  //       return amber500;
  //     case TransactionStatus.failed:
  //       return red500;
  //   }
  // }
}

/// cancelSpeedUp = null (show both buttons) => speed up => (show cancel) cancel ==> Show nothing
/// speedUpCancel = null (show both buttons) => cancel => (show speed up cancellation) speed up  ==> Show speed up cancellation
/// null (show both buttons) => speed up => speed up => speed up ....
/// speedUpCancel = null (show both buttons) => cancel => cancel => cancel ....
@HiveType(typeId: 2)
enum TransactionActions {
  @HiveField(0)
  cancel,
  @HiveField(1)
  speedUp,
  @HiveField(2)
  cancelSpeedUp,
  @HiveField(3)
  speedUpCancel,
}

@HiveType(typeId: 1)
class TransactionModel extends HiveObject {
  // In this state the tx
  factory TransactionModel.fromTransactionReceipt(
    TransactionReceipt receipt,
    String walletAddress,
  ) {
    final txHash = bytesToHex(receipt.transactionHash, include0x: true);

    final timeStamp = DateTime.now();
    final txStatus =
        receipt.status == true
            ? TransactionStatus.done
            : TransactionStatus.pending;
    final txType =
        receipt.from!.hex == walletAddress
            ? TransactionType.sent
            : TransactionType.received;
    // When the receipt is available no need to have these props, since these props are used for
    // speed up & cancel operations.
    final from = receipt.from;
    final to = receipt.to;
    // final feePerGas = receipt.cumulativeGasUsed;
    // final data = receipt.;
    // final gasLimit = receipt.gasUsed?.toInt();

    return TransactionModel(
      hash: txHash,
      timeStamp: timeStamp,
      status: txStatus,
      type: txType,
      value: '0',
      token: null,
      action: null,
      from: from?.hex,
      to: to?.hex,
      // feePerGas: feePerGas.toDouble(), gasLimit: gasLimit,
    );
  }

  // // Does not cover the priority fee
  // factory TransactionModel.fromTransactionInformation(
  //   TransactionInformation transactionInformation,
  //   String walletAddress,
  //   Token token,
  // ) {
  //   final txHash = transactionInformation.hash;
  //   final timeStamp = DateTime.now();
  //   const txStatus = TransactionStatus.pending;
  //   const txType = TransactionType.sent;
  //   final from = transactionInformation.from;
  //   final to = transactionInformation.to;
  //   final feePerGas = transactionInformation.gasPrice;
  //   final data = MXCType.uint8ListToString(transactionInformation.input);
  //   final gasLimit = transactionInformation.gas;
  //   final nonce = transactionInformation.nonce;

  //   return TransactionModel(
  //     hash: txHash,
  //     timeStamp: timeStamp,
  //     status: txStatus,
  //     type: txType,
  //     value: '0',
  //     token: token,
  //     action: null,
  //     from: from.hex,
  //     to: to?.hex,
  //     feePerGas: feePerGas.getInWei.toDouble(),
  //     data: data,
  //     gasLimit: gasLimit,
  //     nonce: nonce,
  //     maxPriorityFee: null,
  //     transferType: null,
  //   );
  // }

  // factory TransactionModel.fromTransaction(
  //   Transaction transaction,
  //   Token? token,
  // ) {
  //   final timeStamp = DateTime.now();
  //   const txStatus = TransactionStatus.pending;
  //   final from = transaction.from;
  //   final to = transaction.to!;
  //   final feePerGas = transaction.maxFeePerGas?.getInWei.toDouble();
  //   final maxPriorityFeePerGas = transaction.maxPriorityFeePerGas?.getInWei;
  //   final gasLimit = transaction.maxGas;
  //   final value = transaction.value?.getInWei.toDouble().toString();
  //   final nonce = transaction.nonce;

  //   final transferType = token?.address != null ? TransferType.erc20 : null;

  //   // For token transfer If It's null It's going to be initialized after tx is done
  //   final data = transaction.data != null
  //       ? MXCType.uint8ListToString(transaction.data!)
  //       : null;

  //   // How about token transfer which contains data but is contract call
  //   final txType = token?.address != null || data == null
  //       ? TransactionType.sent
  //       : TransactionType.contractCall;

  //   return TransactionModel(
  //     hash: '',
  //     timeStamp: timeStamp,
  //     status: txStatus,
  //     type: txType,
  //     value: value,
  //     token: token ?? const Token(),
  //     action: null,
  //     from: from?.hex,
  //     to: to.hex,
  //     feePerGas: feePerGas,
  //     data: data,
  //     gasLimit: gasLimit,
  //     nonce: nonce,
  //     maxPriorityFee: maxPriorityFeePerGas,
  //     transferType: transferType,
  //   );
  // }

  // factory TransactionModel.fromMap(Map<String, dynamic> map) {
  //   return TransactionModel(
  //     timeStamp: map['timeStamp'] != null
  //         ? DateTime.fromMillisecondsSinceEpoch(map['timeStamp'])
  //         : null,
  //     hash: map['hash'],
  //     status: TransactionStatus.values.firstWhere(
  //         (status) => status.toString().split('.').last == map['status']),
  //     type: TransactionType.values
  //         .firstWhere((type) => type.toString().split('.').last == map['type']),
  //     value: map['value'],
  //     token: TokenModel.fromJson(map['token']),
  //     action: map['action'] != null && map['action'] != 'null'
  //         ? TransactionActions.values.firstWhere(
  //             (action) => action.toString().split('.').last == map['action'],
  //           )
  //         : null,
  //     from: map['from'] as String?,
  //     to: map['to'] as String?,
  //     feePerGas: map['feePerGas'] as double?,
  //     data: map['data'] as String?,
  //     gasLimit: map['gasLimit'] as int?,
  //     nonce: map['nonce'] as int?,
  //     maxPriorityFee:
  //         map['maxPriorityFee'] != null && map['maxPriorityFee'] != 'null'
  //             ? BigInt.parse(map['maxPriorityFee'])
  //             : null,
  //     transferType: map['transferType'] != null && map['transferType'] != 'null'
  //         ? TransferType.values.firstWhere(
  //             (transferType) =>
  //                 transferType.toString().split('.').last ==
  //                 map['transferType'],
  //           )
  //         : null,
  //   );
  // }

  TransactionModel({
    required this.hash,
    this.timeStamp,
    required this.status,
    required this.type,
    required this.value,
    this.token,
    required this.action,
    this.from,
    this.to,
    this.feePerGas,
    this.data,
    this.gasLimit,
    this.nonce,
    this.maxPriorityFee,
    this.transferType,
    this.amount,
  });

  TransactionModel copyWith({
    String? hash,
    DateTime? timeStamp,
    TransactionStatus? status,
    TransactionActions? action,
    TransactionType? type,
    String? value,
    TokenItemModel? token,
    String? from,
    String? to,
    double? feePerGas,
    String? data,
    int? gasLimit,
    int? nonce,
    BigInt? maxPriorityFee,
    TransferType? transferType,
  }) {
    return TransactionModel(
      hash: hash ?? this.hash,
      timeStamp: timeStamp ?? this.timeStamp,
      status: status ?? this.status,
      type: type ?? this.type,
      value: value ?? this.value,
      token: token ?? this.token,
      action: action ?? this.action,
      from: from ?? this.from,
      to: to ?? this.to,
      feePerGas: feePerGas ?? this.feePerGas,
      data: data ?? this.data,
      gasLimit: gasLimit ?? this.gasLimit,
      nonce: nonce ?? this.nonce,
      maxPriorityFee: maxPriorityFee ?? this.maxPriorityFee,
      transferType: transferType ?? this.transferType,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'timeStamp': timeStamp?.millisecondsSinceEpoch,
      'hash': hash,
      'status': status.toString().split('.').last,
      'type': type.toString().split('.').last,
      'value': value,
      'token': token?.toJson(),
      'action': action?.toString().split('.').last,
      'from': from,
      'to': to,
      'feePerGas': feePerGas,
      'data': data,
      'gasLimit': gasLimit,
      'nonce': nonce,
      'maxPriorityFee': maxPriorityFee.toString(),
      'transferType': transferType?.toString().split('.').last,
    };
  }

  String toJson() => json.encode(toMap());

  // static TransactionModel fromJson(String source) =>
  //     TransactionModel.fromMap(json.decode(source) as Map<String, dynamic>);

  /// Time details about transaction took place.
  @HiveField(0)
  DateTime? timeStamp;

  /// Hash of the transaction.
  @HiveField(1)
  final String hash;

  /// Whether this transaction was executed successfully.
  @HiveField(2)
  final TransactionStatus status;

  /// The action that are taken over this transaction
  @HiveField(3)
  final TransactionActions? action;

  /// Whether this transaction was sent or received.
  @HiveField(4)
  final TransactionType type;

  /// The value that is transferred in transaction, The unit is in Wei.
  @HiveField(5)
  final String? value;

  final TokenItemModel? token;

  // Below variables are added for speed up & cancel feature
  // pending transactions must contains these props (Some props depending on the case) .
  @HiveField(6)
  final String? from;

  @HiveField(7)
  final String? to;

  @HiveField(8)
  final double? feePerGas;

  @HiveField(9)
  final BigInt? maxPriorityFee;

  @HiveField(10)
  final int? gasLimit;

  @HiveField(11)
  final String? data;

  @HiveField(12)
  final int? nonce;

  // This is for detecting the token transfer from coin transfer in MXC Chains since BlockScout sends value in both cases
  // And that makes hard to detect wether to send the value for speed up.
   @HiveField(14)
  final TransferType? transferType;

  @HiveField(13)
  String? amount;
}
