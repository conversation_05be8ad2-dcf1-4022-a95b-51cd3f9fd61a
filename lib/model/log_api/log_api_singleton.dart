class LogAPIModel {

  LogAPIModel({
    required this.uri,
    required this.method,
    required this.status,
    required this.response,
    required this.body,
    required this.headers,
    this.isError = false,
    this.cURL = '',
  });
  final String uri;
  final String method;
  final String status;
  final String response;
  final String headers;
  final String body;
  final bool isError;
  final String cURL;
  factory LogAPIModel.defaultValue() {
    return LogAPIModel(
      uri: '',
      method: '',
      status: '',
      response: '',
      body: '',
      headers: '',
      cURL: '',
    );
  }
}

class LogAPISingleTon {
  List<LogAPIModel> data = [];
  static final LogAPISingleTon _instance = LogAPISingleTon._internal();

  /// Prmary Constructor for FToast
  factory LogAPISingleTon() {
    return _instance;
  }
  LogAPISingleTon._internal();
}
