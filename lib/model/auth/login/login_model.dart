import 'package:json_annotation/json_annotation.dart';

part 'login_model.g.dart';

@JsonSerializable()
class LoginRequestModel {
  const LoginRequestModel({
    this.email,
    required this.password,
    this.appType = "toii-social",
     this.phoneNumber,
  });

  final String appType;
  final String? email;
  final String? password;

  @J<PERSON><PERSON><PERSON>(name: 'phoneNumber')
  final String? phoneNumber;

  factory LoginRequestModel.fromJson(Map<String, dynamic> json) =>
      _$LoginRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$LoginRequestModelToJson(this);
}

@JsonSerializable()
class LoginModel {
  const LoginModel({
    required this.accessToken,
    required this.expiredAt,
    this.isNewUser,
    required this.refreshToken,
    this.tmpToken
  });

  @JsonKey(name: 'access_token')
  final String accessToken;

  @<PERSON>son<PERSON>ey(name: 'expired_at')
  final String expiredAt;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_new_user')
  final bool? isNewUser;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'refresh_token')
  final String refreshToken;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'tmp_token')
  final String? tmpToken;

  factory LoginModel.fromJson(Map<String, dynamic> json) =>
      _$LoginModelFromJson(json);

  Map<String, dynamic> toJson() => _$LoginModelToJson(this);
}
