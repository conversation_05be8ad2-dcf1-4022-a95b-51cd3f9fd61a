import 'package:json_annotation/json_annotation.dart';

part 'login_wallet.g.dart';

@JsonSerializable()
class WalletLoginRequestModel {
  const WalletLoginRequestModel({
    required this.address,
    required this.signature,
    required this.walletProvider,
  });

  final String address;
  final String signature;
  final String walletProvider;

  factory WalletLoginRequestModel.fromJson(Map<String, dynamic> json) =>
      _$WalletLoginRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$WalletLoginRequestModelToJson(this);
}