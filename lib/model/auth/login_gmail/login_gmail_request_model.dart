import 'package:json_annotation/json_annotation.dart';

part 'login_gmail_request_model.g.dart';

@JsonSerializable()
class LoginGmailRequestModel {
  const LoginGmailRequestModel({
    required this.accessToken,
    required this.idToken,
    this.appType = "toii-social",
    // this.phoneNumber,
  });

  final String appType;
  @JsonKey(name: 'access_token')
  final String accessToken;
  @JsonKey(name: 'id_token')
  final String idToken;


  factory LoginGmailRequestModel.fromJson(Map<String, dynamic> json) =>
      _$LoginGmailRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$LoginGmailRequestModelToJson(this);
}

@JsonSerializable()
class LoginAppleRequestModel {
  const LoginAppleRequestModel({
    required this.idToken,
    this.appType = "toii-social",
    // this.phoneNumber,
  });

  final String appType;
   @JsonKey(name: 'id_token')
  final String idToken;
  


  factory LoginAppleRequestModel.fromJson(Map<String, dynamic> json) =>
      _$LoginAppleRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$LoginAppleRequestModelToJson(this);
}

