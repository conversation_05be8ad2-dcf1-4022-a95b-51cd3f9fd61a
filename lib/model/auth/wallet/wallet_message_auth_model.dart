import 'package:json_annotation/json_annotation.dart';

part 'wallet_message_auth_model.g.dart';

@JsonSerializable()
class WalletMessageAuthModel {
  const WalletMessageAuthModel({
    required this.message,
 
  });

  final String message;
  
  factory WalletMessageAuthModel.fromJson(Map<String, dynamic> json) =>
      _$WalletMessageAuthModelFromJson(json);

  Map<String, dynamic> toJson() => _$WalletMessageAuthModelToJson(this);
}