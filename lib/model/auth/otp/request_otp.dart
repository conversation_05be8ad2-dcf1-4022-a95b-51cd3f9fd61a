import 'package:json_annotation/json_annotation.dart';

part 'request_otp.g.dart';

@JsonSerializable()
class RequestOtpModel {
  final String? email;
  final String? phoneNumber;
  final String purpose;
  final String appType;


  RequestOtpModel({this.email, this.phoneNumber, required this.purpose, this.appType = "toii-social"}); 

  factory RequestOtpModel.fromJson(Map<String, dynamic> json) =>
      _$RequestOtpModelFromJson(json);

  Map<String, dynamic> toJson() => _$RequestOtpModelToJson(this);
}

@JsonSerializable()
class RequestVerifyOtpModel {
  final String? email;
  final String? phoneNumber;
  final String purpose;
  final String otp;
  final String appType;

  RequestVerifyOtpModel({
    this.email,
    this.phoneNumber,
    required this.purpose,
    required this.otp,
    this.appType = "toii-social"
  });

  factory RequestVerifyOtpModel.fromJson(Map<String, dynamic> json) =>
      _$RequestVerifyOtpModelFromJson(json);

  Map<String, dynamic> toJson() => _$RequestVerifyOtpModelToJson(this);
}
