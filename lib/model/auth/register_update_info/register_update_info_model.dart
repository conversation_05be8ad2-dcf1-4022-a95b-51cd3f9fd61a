import 'package:json_annotation/json_annotation.dart';

part 'register_update_info_model.g.dart';

@JsonSerializable()
class RegisterUpdateInfoRequestModel {
  const RegisterUpdateInfoRequestModel({
    this.email,
    this.password,
    this.phoneNumber,
    this.username,
    this.walletAddress,
    this.appType = 'toii-social',
    this.walletProvider = "mykey",
    this.secretKey,
    this.registerType,
    this.networkBase = "ethereum"
  });

  @<PERSON><PERSON><PERSON><PERSON>(name: 'NetworkBase')
  final String networkBase;

  final String? registerType;

  final String? email;
  final String? password;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'phoneNumber')
  final String? phoneNumber;

  final String? username;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'walletAddress')
  final String? walletAddress;

  final String appType;

  final String walletProvider;

  final String? secretKey;

  factory RegisterUpdateInfoRequestModel.fromJson(Map<String, dynamic> json) =>
      _$RegisterUpdateInfoRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$RegisterUpdateInfoRequestModelToJson(this);

  RegisterUpdateInfoRequestModel copyWith({
    String? email,
    String? password,
    String? phoneNumber,
    String? username,
    String? walletAddress,
    String? appType,
    String? walletProvider,
    String? secretKey,
    String? registerType,
  }) {
    return RegisterUpdateInfoRequestModel(
      email: email ?? this.email,
      password: password ?? this.password,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      username: username ?? this.username,
      walletAddress: walletAddress ?? this.walletAddress,
      appType: appType ?? this.appType,
      walletProvider: walletProvider ?? this.walletProvider,
      secretKey: secretKey ?? this.secretKey,
      registerType: registerType ?? this.registerType,
    );
  }
}
