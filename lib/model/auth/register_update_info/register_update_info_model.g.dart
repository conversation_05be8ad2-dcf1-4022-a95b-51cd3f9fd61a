// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'register_update_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RegisterUpdateInfoRequestModel _$RegisterUpdateInfoRequestModelFromJson(
        Map<String, dynamic> json) =>
    RegisterUpdateInfoRequestModel(
      email: json['email'] as String?,
      password: json['password'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
      username: json['username'] as String?,
      walletAddress: json['walletAddress'] as String?,
      appType: json['appType'] as String? ?? 'toii-social',
      walletProvider: json['walletProvider'] as String? ?? "mykey",
      secretKey: json['secretKey'] as String?,
      registerType: json['registerType'] as String?,
      networkBase: json['NetworkBase'] as String? ?? "ethereum",
    );

Map<String, dynamic> _$RegisterUpdateInfoRequestModelToJson(
        RegisterUpdateInfoRequestModel instance) =>
    <String, dynamic>{
      'NetworkBase': instance.networkBase,
      'registerType': instance.registerType,
      'email': instance.email,
      'password': instance.password,
      'phoneNumber': instance.phoneNumber,
      'username': instance.username,
      'walletAddress': instance.walletAddress,
      'appType': instance.appType,
      'walletProvider': instance.walletProvider,
      'secretKey': instance.secretKey,
    };
