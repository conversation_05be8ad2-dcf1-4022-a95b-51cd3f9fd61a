import 'package:json_annotation/json_annotation.dart';

part 'verify_otp_model.g.dart';

@JsonSerializable()
class VerifyOtpResponseModel {
  const VerifyOtpResponseModel({
    required this.expiredAt,
    required this.purpose,
    required this.success,
    required this.tmpToken,
  });

  @J<PERSON><PERSON><PERSON>(name: 'expired_at')
  final String expiredAt;

  final String purpose;
  final bool success;

  @JsonKey(name: 'tmp_token')
  final String tmpToken;

  factory VerifyOtpResponseModel.fromJson(Map<String, dynamic> json) =>
      _$VerifyOtpResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$VerifyOtpResponseModelToJson(this);
}