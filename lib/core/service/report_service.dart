import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:toii_social/model/base/base_response.dart';
import 'package:toii_social/model/report/report_request.dart';

part 'report_service.g.dart';

@RestApi()
abstract class ReportService {
  factory ReportService(Dio dio, {String baseUrl}) = _ReportService;

  @POST('/social/api/v1/reports')
  Future<BaseResponse> submitReport(
    @Body() ReportRequestModel request,
  );
}
