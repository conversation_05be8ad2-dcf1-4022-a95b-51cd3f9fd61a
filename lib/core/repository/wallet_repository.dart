import 'dart:convert';

import 'package:get_it/get_it.dart';
import 'package:tangem_flutter/core/api/price_service.dart';
import 'package:tangem_flutter/core/service/keychain_service.dart';
import 'package:tangem_flutter/core/utils/shared_preferences_manager.dart';
import 'package:tangem_flutter/models/token_price_model.dart';
import 'package:tangem_flutter/models/wallet/wallet_model.dart';

abstract class WalletRepository {
  Future<void> addWallet(WalletModel wallet);
  Future<TokenPriceListsModel> getPriceAll();
  void createPassword({required String password});
  WalletModel? walletCurrent();
  String? getPassword();
  List<WalletModel> listWallet();
  void setWalletCurrent({required String address});
  void changeNameWallet({required WalletModel wallet, required String name});
  void deleteWallet({required WalletModel wallet});
  bool isHaveWallet(String address);
}

class WalletRepositoryImpl implements WalletRepository {
  final walletKey = "KEY_WALLET";
  final passwordKey = "KEY_PASSWORD";
  final currentWalletKey = "KEY_CURRENT_WALLET";
  final PriceService priceService;
  WalletRepositoryImpl({required this.priceService});

  // @override
  // Future<void> addWallet(CardRequestModel request) async {
  //   return await priceService.addWallet(request);
  // }

  @override
  Future<TokenPriceListsModel> getPriceAll() async {
    return await priceService.getPriceAll();
  }

  // @override
  // Future<void> addWallet(CardRequestModel request) async {
  //   return await priceService.addWallet(request);
  // }
  @override
  List<WalletModel> listWallet() {
    final listWalletStr =
        GetIt.instance<SharedPreferencesManager>().getString(walletKey) ?? "";
    Map<String, dynamic> walletJson =
        listWalletStr.isEmpty ? {} : json.decode(listWalletStr);
    List<WalletModel> lists = [];
    walletJson.forEach((key, value) {
      final wallet = WalletModel.fromJson(value);
      lists.add(wallet);
    });

    return lists;
  }

  @override
  Future<void> addWallet(WalletModel wallet) async {
    final listWalletStr =
        GetIt.instance<SharedPreferencesManager>().getString(walletKey) ?? "";
    //  if (listWalletStr?.isNotEmpty == true) {
    Map<String, dynamic> walletJson =
        listWalletStr.isEmpty ? {} : json.decode(listWalletStr);
    walletJson[wallet.publicAddress ?? ""] = wallet.toJson();
    GetIt.instance<SharedPreferencesManager>()
        .putString(walletKey, json.encode(walletJson));
    GetIt.instance<SharedPreferencesManager>()
        .putString(currentWalletKey, wallet.publicAddress!);
  }

  void removeWallet(WalletModel wallet) {
    // final listWalletStr =
    //     GetIt.instance<SharedPreferencesManager>().getString(walletKey);
    // if (listWalletStr?.isNotEmpty == true) {
    //   List<dynamic> walletJson = json.decode(listWalletStr!);
    //   final listWallet =
    //       walletJson.map((e) => WalletModel.fromJson(e)).toList();
    //   listWallet
    //       .removeWhere((item) => item.publicAddress == wallet.publicAddress);

    //   final newWallet =
    //       jsonEncode(listWallet.map((e) => e.toJson()).toList()).toString();
    //   GetIt.instance<SharedPreferencesManager>()
    //       .putString(walletKey, newWallet);
    //  }
  }
  @override
  WalletModel? walletCurrent() {
    final walletCurrent = GetIt.instance<SharedPreferencesManager>()
            .getString(currentWalletKey) ??
        "";
    if (walletCurrent.isEmpty) {
      return null;
    }
    final listWalletStr =
        GetIt.instance<SharedPreferencesManager>().getString(walletKey) ?? "";
    if (listWalletStr.isEmpty) {
      return null;
    }
    Map<String, dynamic> walletJson =
        listWalletStr.isEmpty ? {} : json.decode(listWalletStr);
    if (walletJson[walletCurrent] != null) {
      final wallet = WalletModel.fromJson(walletJson[walletCurrent]);
      return wallet;
    }
    return null;
  }

  @override
  bool isHaveWallet(String address) {
    final listWalletStr =
        GetIt.instance<SharedPreferencesManager>().getString(walletKey) ?? "";
    Map<String, dynamic> walletJson =
        listWalletStr.isEmpty ? {} : json.decode(listWalletStr);
    if (walletJson[address] != null) {
      return true;
    } else {
      return false;
    }
  }

  void removeAllWalet() {}

  @override
  void createPassword({required String password}) {
    GetIt.instance<SharedPreferencesManager>().putString(passwordKey, password);
  }

  @override
  String? getPassword() {
    return GetIt.instance<SharedPreferencesManager>().getString(passwordKey);
  }

  @override
  void setWalletCurrent({required String address}) {
    GetIt.instance<SharedPreferencesManager>()
        .putString(currentWalletKey, address);
  }

  @override
  void changeNameWallet({required WalletModel wallet, required String name}) {
    final listWalletStr =
        GetIt.instance<SharedPreferencesManager>().getString(walletKey) ?? "";
    Map<String, dynamic> walletJson =
        listWalletStr.isEmpty ? {} : json.decode(listWalletStr);

    walletJson[wallet.publicAddress ?? ""] =
        wallet.copyWith(name: name).toJson();

    GetIt.instance<SharedPreferencesManager>()
        .putString(walletKey, json.encode(walletJson));
    GetIt.instance<SharedPreferencesManager>()
        .putString(currentWalletKey, wallet.publicAddress!);
  }

  @override
  void deleteWallet({required WalletModel wallet}) {
    final listWalletStr =
        GetIt.instance<SharedPreferencesManager>().getString(walletKey) ?? "";
    Map<String, dynamic> walletJson =
        listWalletStr.isEmpty ? {} : json.decode(listWalletStr);

    walletJson.remove(wallet.publicAddress ?? "");
    final walletCurrent = GetIt.instance<SharedPreferencesManager>()
            .getString(currentWalletKey) ??
        "";
    //  {
    if (walletJson.isEmpty) {
      KeychainService.instance.clearData();
    } else if (wallet.publicAddress == walletCurrent) {
      setWalletCurrent(address: walletJson.keys.first);
    }
    KeychainService.instance.deleteWallet(wallet.publicAddress!);
    GetIt.instance<SharedPreferencesManager>()
        .putString(walletKey, json.encode(walletJson));
  }
}
