import 'package:toii_social/core/service/report_service.dart';
import 'package:toii_social/model/base/base_response.dart';
import 'package:toii_social/model/report/report_request.dart';

abstract class ReportRepository {
  Future<BaseResponse> submitReport(ReportRequestModel request);
}

class ReportRepositoryImpl extends ReportRepository {
  final ReportService reportService;

  ReportRepositoryImpl({required this.reportService});

  @override
  Future<BaseResponse> submitReport(ReportRequestModel request) {
    return reportService.submitReport(request);
  }
}
