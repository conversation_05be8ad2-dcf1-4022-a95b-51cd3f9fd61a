import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/report_repository.dart';
import 'package:toii_social/cubit/report/report_state.dart';
import 'package:toii_social/model/report/report_request.dart';
import 'package:toii_social/screen/home/<USER>/bottom_sheet_report.dart';

class ReportCubit extends Cubit<ReportState> {
  ReportCubit() : super(const ReportState());

  final ReportRepository _reportRepository = GetIt.instance<ReportRepository>();

  /// Submit a report for a post
  Future<void> reportPost({
    required String postId,
    required List<ReportReason> reasons,
    String? customReason,
  }) async {
    await _submitReport(
      itemId: postId,
      itemType: 'post',
      reasons: reasons,
      customReason: customReason,
    );
  }

  /// Submit a report for a comment
  Future<void> reportComment({
    required String commentId,
    required List<ReportReason> reasons,
    String? customReason,
  }) async {
    await _submitReport(
      itemId: commentId,
      itemType: 'comment',
      reasons: reasons,
      customReason: customReason,
    );
  }

  /// Internal method to handle report submission
  Future<void> _submitReport({
    required String itemId,
    required String itemType,
    required List<ReportReason> reasons,
    String? customReason,
  }) async {
    try {
      emit(state.copyWith(status: ReportStatus.loading));
      EasyLoading.show();
      // Prepare the description
      String description = customReason ?? '';
      if (description.isEmpty) {
        description = reasons.map((r) => r.displayName).join(', ');
      }

      // Create the request
      final request = ReportRequestModel(
        description: description,
        reasons: reasons.map((r) => r.apiValue).toList(),
        reportedItemId: itemId,
        reportedItemType: itemType,
      );

      // Submit the report
      await _reportRepository.submitReport(request);

      emit(state.copyWith(status: ReportStatus.success, errorMessage: null));
    } catch (e) {
      emit(
        state.copyWith(
          status: ReportStatus.failure,
          errorMessage: "report failed",
        ),
      );
    } finally {
      EasyLoading.dismiss();
    }
  }

  /// Reset the state to initial
  void reset() {
    emit(const ReportState());
  }
}
