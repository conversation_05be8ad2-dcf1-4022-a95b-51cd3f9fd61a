part of 'follower_cubit.dart';

enum FollowerStatus { initial, loading, success, failure }

extension FollowerX on FollowerStatus {
  bool get isInitial => this == FollowerStatus.initial;
  bool get isLoading => this == FollowerStatus.loading;
  bool get isSuccess => this == FollowerStatus.success;
  bool get isFailure => this == FollowerStatus.failure;
}

final class FollowerState {
  final List<UserModel> followers;
  final List<UserModel> following;
  final int totalFollowers;
  final int totalFollowing;
  final FollowerStatus status;
  final String? errorMessage;

  const FollowerState({
    this.followers = const [],
    this.following = const [],
    this.totalFollowers = 0,
    this.totalFollowing = 0,
    this.status = FollowerStatus.initial,
    this.errorMessage,
  });

  FollowerState copyWith({
    List<UserModel>? followers,
    List<UserModel>? following,
    int? totalFollowers,
    int? totalFollowing,
    FollowerStatus? status,
    String? errorMessage,
  }) {
    return FollowerState(
      followers: followers ?? this.followers,
      following: following ?? this.following,
      totalFollowers: totalFollowers ?? this.totalFollowers,
      totalFollowing: totalFollowing ?? this.totalFollowing,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}
