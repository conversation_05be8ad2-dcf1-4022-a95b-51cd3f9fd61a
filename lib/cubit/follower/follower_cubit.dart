import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/social_repository.dart';
import 'package:toii_social/model/user/user_model.dart';

part 'follower_state.dart';

class FollowerCubit extends Cubit<FollowerState> {
  FollowerCubit() : super(const FollowerState());

  final SocialRepository _socialRepository = GetIt.instance<SocialRepository>();

  // Fake data generators
  List<UserModel> _generateFakeFollowers() {
    return [
      UserModel(
        id: 'fake_follower_1',
        username: 'john_doe',
        lastName: 'Doe',
        fullName: '<PERSON>',
        avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
        address: null,
        moreInfo: {},
        lastActivity: DateTime.now().subtract(const Duration(hours: 2)),
        role: 'user',
        status: 'active',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
        firstName: '<PERSON>',
        email: '<EMAIL>',
        phoneNumber: '+1234567890',
        walletAddress: '0x1234567890abcdef',
      ),
      UserModel(
        id: 'fake_follower_2',
        username: 'jane_smith',
        lastName: 'Smith',
        fullName: 'Jane Smith',
        avatar: 'https://randomuser.me/api/portraits/women/2.jpg',
        address: null,
        moreInfo: {},
        lastActivity: DateTime.now().subtract(const Duration(hours: 5)),
        role: 'user',
        status: 'active',
        createdAt: DateTime.now().subtract(const Duration(days: 45)),
        updatedAt: DateTime.now(),
        firstName: 'Jane',
        email: '<EMAIL>',
        phoneNumber: '+1234567891',
        walletAddress: '0x1234567890abcde1',
      ),
      UserModel(
        id: 'fake_follower_3',
        username: 'alex_wilson',
        lastName: 'Wilson',
        fullName: 'Alex Wilson',
        avatar: 'https://randomuser.me/api/portraits/men/3.jpg',
        address: null,
        moreInfo: {},
        lastActivity: DateTime.now().subtract(const Duration(days: 1)),
        role: 'user',
        status: 'active',
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        updatedAt: DateTime.now(),
        firstName: 'Alex',
        email: '<EMAIL>',
        phoneNumber: '+1234567892',
        walletAddress: '0x1234567890abcde2',
      ),
      UserModel(
        id: 'fake_follower_4',
        username: 'sarah_johnson',
        lastName: 'Johnson',
        fullName: 'Sarah Johnson',
        avatar: 'https://randomuser.me/api/portraits/women/4.jpg',
        address: null,
        moreInfo: {},
        lastActivity: DateTime.now().subtract(const Duration(hours: 12)),
        role: 'user',
        status: 'active',
        createdAt: DateTime.now().subtract(const Duration(days: 60)),
        updatedAt: DateTime.now(),
        firstName: 'Sarah',
        email: '<EMAIL>',
        phoneNumber: '+1234567893',
        walletAddress: '0x1234567890abcde3',
      ),
      UserModel(
        id: 'fake_follower_5',
        username: 'mike_brown',
        lastName: 'Brown',
        fullName: 'Mike Brown',
        avatar: 'https://randomuser.me/api/portraits/men/5.jpg',
        address: null,
        moreInfo: {},
        lastActivity: DateTime.now().subtract(const Duration(hours: 8)),
        role: 'user',
        status: 'active',
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
        updatedAt: DateTime.now(),
        firstName: 'Mike',
        email: '<EMAIL>',
        phoneNumber: '+1234567894',
        walletAddress: '0x1234567890abcde4',
      ),
    ];
  }

  List<UserModel> _generateFakeFollowing() {
    return [
      UserModel(
        id: 'fake_following_1',
        username: 'crypto_guru',
        lastName: 'Master',
        fullName: 'Crypto Master',
        avatar: 'https://randomuser.me/api/portraits/men/10.jpg',
        address: null,
        moreInfo: {},
        lastActivity: DateTime.now().subtract(const Duration(minutes: 30)),
        role: 'influencer',
        status: 'active',
        createdAt: DateTime.now().subtract(const Duration(days: 100)),
        updatedAt: DateTime.now(),
        firstName: 'Crypto',
        email: '<EMAIL>',
        phoneNumber: '+1234567800',
        walletAddress: '0x1234567890abcd00',
      ),
      UserModel(
        id: 'fake_following_2',
        username: 'nft_artist',
        lastName: 'Artist',
        fullName: 'NFT Artist',
        avatar: 'https://randomuser.me/api/portraits/women/11.jpg',
        address: null,
        moreInfo: {},
        lastActivity: DateTime.now().subtract(const Duration(hours: 3)),
        role: 'artist',
        status: 'active',
        createdAt: DateTime.now().subtract(const Duration(days: 80)),
        updatedAt: DateTime.now(),
        firstName: 'NFT',
        email: '<EMAIL>',
        phoneNumber: '+1234567801',
        walletAddress: '0x1234567890abcd01',
      ),
      UserModel(
        id: 'fake_following_3',
        username: 'web3_dev',
        lastName: 'Developer',
        fullName: 'Web3 Developer',
        avatar: 'https://randomuser.me/api/portraits/men/12.jpg',
        address: null,
        moreInfo: {},
        lastActivity: DateTime.now().subtract(const Duration(hours: 1)),
        role: 'developer',
        status: 'active',
        createdAt: DateTime.now().subtract(const Duration(days: 50)),
        updatedAt: DateTime.now(),
        firstName: 'Web3',
        email: '<EMAIL>',
        phoneNumber: '+1234567802',
        walletAddress: '0x1234567890abcd02',
      ),
    ];
  }

  void getFollowers() async {
    try {
      emit(state.copyWith(status: FollowerStatus.loading));

      final result = await _socialRepository.getFollowers();

      // If API returns empty data, use fake data
      if (result.data.followers.isEmpty) {
        final fakeFollowers = _generateFakeFollowers();
        emit(
          state.copyWith(
            status: FollowerStatus.success,
            followers: fakeFollowers,
            totalFollowers: fakeFollowers.length,
          ),
        );
      } else {
        emit(
          state.copyWith(
            status: FollowerStatus.success,
            followers: result.data.followers,
            totalFollowers: result.data.total,
          ),
        );
      }
    } catch (e) {
      // If API fails, keep the original error handling behavior
      emit(
        state.copyWith(
          status: FollowerStatus.failure,
          errorMessage: e.toString(),
        ),
      );
    }
  }

  void getFollowing() async {
    try {
      emit(state.copyWith(status: FollowerStatus.loading));

      final result = await _socialRepository.getFollowing();

      // If API returns empty data, use fake data
      if (result.data.following.isEmpty) {
        final fakeFollowing = _generateFakeFollowing();
        emit(
          state.copyWith(
            status: FollowerStatus.success,
            following: fakeFollowing,
            totalFollowing: fakeFollowing.length,
          ),
        );
      } else {
        emit(
          state.copyWith(
            status: FollowerStatus.success,
            following: result.data.following,
            totalFollowing: result.data.total,
          ),
        );
      }
    } catch (e) {
      // If API fails, keep the original error handling behavior
      emit(
        state.copyWith(
          status: FollowerStatus.failure,
          errorMessage: e.toString(),
        ),
      );
    }
  }

  void refreshFollowers() {
    getFollowers();
  }

  void refreshFollowing() {
    getFollowing();
  }
}
