part of 'create_password_validate_cubit.dart';

final class CreatePasswordValidateState extends Equatable {
  final String? errorEmail;
  final String? errorPassword;
  final bool isShowPassword;
  final bool isShowConfirmPassword;
  final bool have8Characters;
  final bool containsUppercase;
  final bool haveSpecialCharacter;
  final String? errorConfirmPassword;
  final bool isEnableButton;
  final bool isEnableBiometric;
  const CreatePasswordValidateState({
    this.errorEmail,
    this.errorPassword,
    this.isShowConfirmPassword = false,
    this.isShowPassword = false,
    this.have8Characters = false,
    this.containsUppercase = false,
    this.haveSpecialCharacter = false,
    this.errorConfirmPassword,
    this.isEnableButton = false,
    this.isEnableBiometric = true,
  });

  @override
  List<Object?> get props => [
    errorEmail,
    errorPassword,
    isShowPassword,
    isShowConfirmPassword,
    have8<PERSON>haracters,
    containsUppercase,
    haveSpecial<PERSON>haracter,
    errorConfirmPassword,
    isEnableButton,
    isEnableBiometric
  ];

  CreatePasswordValidateState copyWith({
    String? errorEmail,
    String? errorPassword,
    bool? isShowPassword,
    bool? isShowConfirmPassword,
    bool? have8Characters,
    bool? containsUppercase,
    bool? haveSpecialCharacter,
    String? errorConfirmPassword,
    bool? isEnableButton,
    bool? isEnableBiometric,
  }) {
    return CreatePasswordValidateState(
      errorEmail: errorEmail ?? this.errorEmail,
      errorPassword: errorPassword ?? this.errorPassword,
      isShowPassword: isShowPassword ?? this.isShowPassword,
      isShowConfirmPassword: isShowConfirmPassword ?? this.isShowConfirmPassword,
      have8Characters: have8Characters ?? this.have8Characters,
      containsUppercase: containsUppercase ?? this.containsUppercase,
      haveSpecialCharacter: haveSpecialCharacter ?? this.haveSpecialCharacter,
      errorConfirmPassword: errorConfirmPassword ?? this.errorConfirmPassword,
      isEnableButton: isEnableButton ?? this.isEnableButton,
      isEnableBiometric: isEnableBiometric ?? this.isEnableBiometric,
    );
  }
}
