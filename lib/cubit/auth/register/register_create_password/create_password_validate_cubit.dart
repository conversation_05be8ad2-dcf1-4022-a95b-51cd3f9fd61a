import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_social/utils/utils/validator.dart';

part 'create_password_validate_state.dart';

class CreatePasswordValidateCubit extends Cubit<CreatePasswordValidateState> {
  CreatePasswordValidateCubit() : super(const CreatePasswordValidateState());

  bool validateForm({String? password, String? confirmPassword}) {
    bool isValid = true;
    if (password != null) {
      if (password.isEmpty) {
        emit(state.copyWith(errorPassword: "Please input password"));
        isValid = false;
      } else if (password.length < 8) {
        emit(
          state.copyWith(
            errorPassword: "Password must be at least 8 characters",
          ),
        );
        isValid = false;
      } else if (!password.containsUppercase) {
        emit(
          state.copyWith(
            errorPassword:
                "Password must contain at least 1 uppercase character",
          ),
        );
        isValid = false;
      } else if (!password.containsSpecialCharacter) {
        emit(
          state.copyWith(
            errorPassword: "Password must contain at least 1 special character",
          ),
        );

        isValid = false;
      } else {
        emit(state.copyWith(errorPassword: ""));
      }
    }
    if (confirmPassword != null) {
      if (confirmPassword.isEmpty) {
        emit(
          state.copyWith(errorConfirmPassword: "Please input confirm password"),
        );
        isValid = false;
      } else if (confirmPassword != password) {
        emit(
          state.copyWith(
            errorConfirmPassword: "Confirm password does not match",
          ),
        );
        isValid = false;
      } else {
        emit(state.copyWith(errorConfirmPassword: ""));
      }
    }
    return isValid;
  }


  void validateValidPassword({required String password, required String confirmPassword}) {
    emit(
      state.copyWith(
        isEnableButton:  password.length >= 8 && password.containsUppercase &&
            password.containsSpecialCharacter &&
            confirmPassword == password,
        have8Characters: password.length >= 8,
        containsUppercase: password.containsUppercase,
        haveSpecialCharacter: password.containsSpecialCharacter,
      ),
    );
  }

  void clearErrorPassword() {
    emit(state.copyWith(errorPassword: ""));
  }

  void toggleShowPassword() {
    emit(state.copyWith(isShowPassword: !state.isShowPassword));
  }

  void toggleShowConfirmPassword() {
    emit(state.copyWith(isShowConfirmPassword: !state.isShowConfirmPassword));
  }

  void toggleOnBiometric() {
    emit(state.copyWith(isEnableBiometric: !state.isEnableBiometric));
  }
}
