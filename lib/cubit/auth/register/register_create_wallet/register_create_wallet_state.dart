part of 'register_create_wallet_cubit.dart';


enum RegisterCreateWalletStatus { initial, loading, success, failure }

extension RegisterCreateWalletX on RegisterCreateWalletStatus {
  bool get isInitial => this == RegisterCreateWalletStatus.initial;
  bool get isLoading => this == RegisterCreateWalletStatus.loading;
  bool get isSuccess => this == RegisterCreateWalletStatus.success;
  bool get isFailure => this == RegisterCreateWalletStatus.failure;
}

final class RegisterCreateWalletState {
  final UserModel? userModel;
  final RegisterCreateWalletStatus status;
  final String? errorMessage;

  const RegisterCreateWalletState({
    this.userModel,
    this.status = RegisterCreateWalletStatus.initial,
    this.errorMessage,
  });

  RegisterCreateWalletState copyWith({
    UserModel? userModel,
    RegisterCreateWalletStatus? status,
    String? errorMessage,
  }) {
    return RegisterCreateWalletState(
      userModel: userModel ?? this.userModel,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}