import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/constant/key_shared.dart';
import 'package:toii_social/core/repository/auth_repository.dart';
import 'package:toii_social/core/service/wallet_service.dart';
import 'package:toii_social/model/auth/register_update_info/register_update_info_model.dart';
import 'package:toii_social/model/user/user_model.dart';
import 'package:toii_social/utils/keychain/keychain_service.dart';
import 'package:toii_social/utils/shared_prefs/shared_prefs.dart';

part 'register_create_wallet_state.dart';

class RegisterCreateWalletCubit extends Cubit<RegisterCreateWalletState> {
  final RegisterUpdateInfoRequestModel requestInfo;
  RegisterCreateWalletCubit({required this.requestInfo})
    : super(const RegisterCreateWalletState());
  final AuthRepository _authRepository = GetIt.instance<AuthRepository>();

  void completeRegistration() async {
    final wallet = await WalletService.createWallet();
    // final privateKey = WalletService.enCryptingData(
    //   plainText: wallet.privateKeyHex,
    //   keyEncrypt: pin,
    // );
    final request = requestInfo.copyWith(
      walletAddress: wallet.address,
      //  secretKey: privateKey,
    );
    try {
      emit(state.copyWith(status: RegisterCreateWalletStatus.loading));

      final result = await _authRepository.completeRegistration(request);
      KeychainService.instance.setWallet(credentials: wallet);
      SharedPref.setString(KeyShared.tokenKey, result.data.accessToken);
      SharedPref.setBool(KeyShared.isLogin, true);
      getProfile();
    } on DioException catch (e) {
      emit(
        state.copyWith(
          status: RegisterCreateWalletStatus.failure,
          errorMessage: e.toString(),
        ),
      );
    } on Exception catch (_) {
      emit(
        state.copyWith(
          status: RegisterCreateWalletStatus.failure,
          errorMessage: "Something went wrong",
        ),
      );
    }
  }

  void completeRegistrationWallet({required String userName}) async {
    try {
      emit(state.copyWith(status: RegisterCreateWalletStatus.loading));
      final request = requestInfo.copyWith(
        username: userName,
        registerType: "wallet",
        walletAddress: SharedPref.getString(KeyShared.walletCurrentKey),
        //  secretKey: privateKey,
      );
      final result = await _authRepository.completeRegistration(request);
      SharedPref.setString(KeyShared.tokenKey, result.data.accessToken);
      SharedPref.setBool(KeyShared.isLogin, true);
      getProfile();
    } on DioException catch (e) {
      emit(
        state.copyWith(
          status: RegisterCreateWalletStatus.failure,
          errorMessage: e.toString(),
        ),
      );
    } on Exception catch (_) {
      emit(
        state.copyWith(
          status: RegisterCreateWalletStatus.failure,
          errorMessage: "Something went wrong",
        ),
      );
    }
  }

  void getProfile() async {
    try {
      final result = await _authRepository.getProfile();
      emit(
        state.copyWith(
          status: RegisterCreateWalletStatus.success,
          userModel: result.data,
        ),
      );
    } on DioException catch (e) {
      emit(
        state.copyWith(
          status: RegisterCreateWalletStatus.failure,
          errorMessage: e.toString(),
        ),
      );
    } on Exception catch (_) {
      emit(
        state.copyWith(
          status: RegisterCreateWalletStatus.failure,
          errorMessage: "Something went wrong",
        ),
      );
    }
  }
}
