part of 'register_choose_country_cubit.dart';

final class RegisterChooseCountryState {
  final String countryCode;
  final String? phoneNumber;
  final bool isLoading;

  const RegisterChooseCountryState({
    this.countryCode = 'US',
    this.phoneNumber,
    this.isLoading = false,
  });

  RegisterChooseCountryState copyWith({
    String? countryCode,
    String? phoneNumber,
    bool? isLoading,
  }) {
    return RegisterChooseCountryState(
      countryCode: countryCode ?? this.countryCode,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}