import 'package:country_code_picker/country_code_picker.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'register_choose_country_state.dart';

class RegisterChooseCountryCubit extends Cubit<RegisterChooseCountryState> {
  RegisterChooseCountryCubit() : super(const RegisterChooseCountryState());

  CountryCode get country {
    List<Map<String, String>> jsonList = codes;

    List<CountryCode> elements =
        jsonList.map((json) => CountryCode.fromJson(json)).toList();
    return elements.firstWhere(
      (criteria) =>
          (criteria.code!.toUpperCase() == state.countryCode.toUpperCase()),
      orElse: () => elements[0],
    );
  }

  void setCountryCode(String countryCode) {
    emit(
      state.copyWith(
        countryCode: countryCode,
      ),
    );
  }
}
