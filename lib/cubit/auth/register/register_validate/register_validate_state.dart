part of 'register_validate_cubit.dart';

final class RegisterValidateState extends Equatable {
  final String? errorEmail;
  final String? errorPhone;
  final bool isAgreed;
  final bool isEnableButton;

  const RegisterValidateState({
    this.errorEmail,
    this.errorPhone,
    this.isAgreed = false,
    this.isEnableButton = false,
  });

  @override
  List<Object?> get props => [errorEmail, errorPhone, isAgreed, isEnableButton];

  RegisterValidateState copyWith({
    String? errorEmail,
    String? errorPhone,
    bool? isAgreed,
    bool? isEnableButton,
  }) {
    return RegisterValidateState(
      errorEmail: errorEmail ?? this.errorEmail,
      errorPhone: errorPhone ?? this.errorPhone,
      isAgreed: isAgreed ?? this.isAgreed,
      isEnableButton: isEnableButton ?? this.isEnableButton,
    );
  }
}
