import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_social/utils/utils/validator.dart';

part 'register_validate_state.dart';

class RegisterValidateCubit extends Cubit<RegisterValidateState> {
  RegisterValidateCubit() : super(const RegisterValidateState());

  bool validateForm({
    String? email,
    String? phoneNumber,
    bool isValidateUI = true,
  }) {
    bool isValid = true;
    if (email != null) {
      if (email.isEmpty) {
        if (isValidateUI) {
          emit(state.copyWith(errorEmail: "Please input email"));
        }

        isValid = false;
      } else if (email.isValidEmail() == false) {
        if (isValidateUI) {
          emit(state.copyWith(errorEmail: "Email invalid"));
        }
        isValid = false;
      } else {
        emit(state.copyWith(errorEmail: ""));
      }
    }
    if (phoneNumber != null) {
      if (phoneNumber.isEmpty) {
        if (isValidateUI) {
          emit(state.copyWith(errorPhone: "Please input phone number"));
        }
        isValid = false;
      } else if (phoneNumber.isPhoneNoValid == false) {
        if (isValidateUI) {
          emit(state.copyWith(errorPhone: "Phone number invalid"));
        }
        isValid = false;
      } else {
        emit(state.copyWith(errorPhone: ""));
      }
    }
    return isValid;
  }

  void clearErrorEmail() {
    emit(state.copyWith(errorEmail: ""));
  }

  void clearErrorPhone() {
    emit(state.copyWith(errorPhone: ""));
  }

  // void setErrorWhenLogin(String error) {
  //   emit(state.copyWith(errorPhone: error));
  // }

  void setAgreed() {
    emit(state.copyWith(isAgreed: !state.isAgreed));
  }

  void checkEnableButton(bool isPhone, {String? email, String? phoneNumber}) {
    final bool isValidate;
    if (isPhone) {
      isValidate = validateForm(phoneNumber: phoneNumber, isValidateUI: false);
    } else {
      isValidate = validateForm(email: email, isValidateUI: false);
    }
    emit(state.copyWith(isEnableButton: isValidate && state.isAgreed));
  }
}
