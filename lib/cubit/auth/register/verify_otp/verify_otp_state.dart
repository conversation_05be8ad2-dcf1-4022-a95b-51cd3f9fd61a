part of 'verify_otp_cubit.dart';

enum VerifyOtpStatus { initial, loading, success, failure }

extension VerifyOtpX on VerifyOtpStatus {
  bool get isInitial => this == VerifyOtpStatus.initial;
  bool get isLoading => this == VerifyOtpStatus.loading;
  bool get isSuccess => this == VerifyOtpStatus.success;
  bool get isFailure => this == VerifyOtpStatus.failure;
}

final class VerifyOtpState {
  final String phoneNumber;
  final VerifyOtpStatus status;
  final String? errorMessage;

  const VerifyOtpState({
    this.phoneNumber = '',
    this.status = VerifyOtpStatus.initial  ,
    this.errorMessage,
  });

  VerifyOtpState copyWith({
    String? phoneNumber,
    VerifyOtpStatus? status,
    String? errorMessage,
  }) {
    return VerifyOtpState(
      phoneNumber: phoneNumber ?? this.phoneNumber,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}