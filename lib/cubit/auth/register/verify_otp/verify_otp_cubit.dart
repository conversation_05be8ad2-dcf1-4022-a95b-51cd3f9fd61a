import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/constant/key_shared.dart';
import 'package:toii_social/core/repository/auth_repository.dart';
import 'package:toii_social/model/auth/otp/request_otp.dart';
import 'package:toii_social/utils/shared_prefs/shared_prefs.dart';

part 'verify_otp_state.dart';

class VerifyOtpCubit extends Cubit<VerifyOtpState> {
  VerifyOtpCubit() : super(const VerifyOtpState());

  final AuthRepository _authRepository = GetIt.instance<AuthRepository>();
  void verifyOtp({
    String? email,
    String? phoneNumber,
    required String otp,
  }) async {
    try {
      emit(state.copyWith(status: VerifyOtpStatus.loading));
      final request = RequestVerifyOtpModel(
        email: email?.trim().toLowerCase(),
        purpose: 'register',
        phoneNumber: phoneNumber?.trim(),
        otp: otp,
      );

      final result = await _authRepository.verifyOtp(request);
      SharedPref.setString(KeyShared.tmpTokenKey, result.data.tmpToken);

      emit(state.copyWith(status: VerifyOtpStatus.success));
    } on DioException catch (e) {
      emit(
        state.copyWith(
          status: VerifyOtpStatus.failure,
          errorMessage: e.toString(),
        ),
      );
    } on Exception catch (_) {
      emit(
        state.copyWith(
          status: VerifyOtpStatus.failure,
          errorMessage: "Something went wrong",
        ),
      );
    }
  }
}
