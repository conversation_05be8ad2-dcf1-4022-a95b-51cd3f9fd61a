import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/auth_repository.dart';
import 'package:toii_social/model/auth/otp/request_otp.dart';
import 'package:toii_social/utils/utils/validator.dart';

part 'register_request_otp_state.dart';

class RegisterRequestOtpCubit extends Cubit<RegisterRequestOtpState> {
  RegisterRequestOtpCubit() : super(const RegisterRequestOtpState());

  final AuthRepository _authRepository = GetIt.instance<AuthRepository>();
  void requestOtpPhone({
    required String code,
    required String phoneNumber,
  }) async {
    try {
      final formattedPhoneNumber =
          code.trim() + phoneNumber.formattedPhoneNumber;

      emit(state.copyWith(status: RegisterRequestOtpStatus.loading));
      final request = RequestOtpModel(
        phoneNumber: formattedPhoneNumber,
        purpose: 'register',
      );
      final _ = await _authRepository.requestOtp(request);
      emit(state.copyWith(status: RegisterRequestOtpStatus.success));
    } on DioException catch (e) {
      emit(
        state.copyWith(
          status: RegisterRequestOtpStatus.failure,
          errorMessage: e.toString(),
        ),
      );
    } on Exception catch (_) {
      emit(
        state.copyWith(
          status: RegisterRequestOtpStatus.failure,
          errorMessage: "Something went wrong",
        ),
      );
    }
  }

  void requestOtpEmail({required String email}) async {
    try {
      emit(state.copyWith(status: RegisterRequestOtpStatus.loading));
      final request = RequestOtpModel(
        email: email.trim().toLowerCase(),
        purpose: 'register',
      );
      final _ = await _authRepository.requestOtp(request);
      emit(state.copyWith(status: RegisterRequestOtpStatus.success));
    } on DioException catch (e) {
      emit(
        state.copyWith(
          status: RegisterRequestOtpStatus.failure,
          errorMessage: e.toString(),
        ),
      );
    } on Exception catch (_) {
      emit(
        state.copyWith(
          status: RegisterRequestOtpStatus.failure,
          errorMessage: "Something went wrong",
        ),
      );
    }
  }
}
