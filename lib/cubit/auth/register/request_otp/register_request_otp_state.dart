part of 'register_request_otp_cubit.dart';

enum RegisterRequestOtpStatus { initial, loading, success, failure }

extension RegisterRequestOtpX on RegisterRequestOtpStatus {
  bool get isInitial => this == RegisterRequestOtpStatus.initial;
  bool get isLoading => this == RegisterRequestOtpStatus.loading;
  bool get isSuccess => this == RegisterRequestOtpStatus.success;
  bool get isFailure => this == RegisterRequestOtpStatus.failure;
}

final class RegisterRequestOtpState {
  final String phoneNumber;
  final RegisterRequestOtpStatus status;
  final String? errorMessage;

  const RegisterRequestOtpState({
    this.phoneNumber = '',
    this.status = RegisterRequestOtpStatus.initial  ,
    this.errorMessage,
  });

  RegisterRequestOtpState copyWith({
    String? phoneNumber,
    RegisterRequestOtpStatus? status,
    String? errorMessage,
  }) {
    return RegisterRequestOtpState(
      phoneNumber: phoneNumber ?? this.phoneNumber,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}