import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_social/model/auth/register_update_info/register_update_info_model.dart';

class RegisterVerifyStepCubit extends Cubit<int> {
  var requestInfo = RegisterUpdateInfoRequestModel();
   bool isSocial = false;
  var pin = "";
  RegisterVerifyStepCubit() : super(1);
  void nextStep() {
    emit(state + 1);
  }

  updateRequestInfo({
    String? phoneNumber,
    String? email,
    String? userName,
    String? password,
    String? walletAddress,
    String? registerType,
  }) {
    requestInfo = requestInfo.copyWith(
      phoneNumber: phoneNumber,
      email: email,
      username: userName,
      password: password,
      registerType: registerType,
      walletAddress: walletAddress,
    );
  }
}
