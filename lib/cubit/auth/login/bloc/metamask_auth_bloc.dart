// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:get_it/get_it.dart';
// import 'package:toii_social/core/service/metamask/wallet_connector_service.dart';
// import 'package:toii_social/cubit/auth/login/bloc/wallet_event.dart';
// import 'package:toii_social/cubit/auth/login/bloc/wallet_state.dart';
// import 'package:walletconnect_flutter_v2/apis/sign_api/models/session_models.dart';
// import 'package:walletconnect_flutter_v2/apis/sign_api/models/sign_client_models.dart';
// import 'package:walletconnect_flutter_v2/apis/utils/namespace_utils.dart';

// class MetaMaskAuthBloc extends Bloc<WalletEvent, WalletState> {
//   final WalletConnectorService walletConnectorService = GetIt.instance<WalletConnectorService>();

//   MetaMaskAuthBloc() : super(WalletInitialState()) {
//     on<MetamaskAuthEvent>((event, emit) async {
//       try {
//         emit(WalletInitializedState(message: AppConstants.initializing));

//         bool isInitialize = await walletConnectorService.initialize();
//         if (!isInitialize) {
//           emit(WalletErrorState(message: AppConstants.walletConnectError));
//           return;
//         }

//         emit(WalletInitializedState(message: AppConstants.initialized));

//         ConnectResponse? resp = await walletConnectorService.connect();
//         if (resp == null || resp.uri == null) {
//           emit(WalletErrorState(message: AppConstants.walletConnectError));
//           return;
//         }

//         bool canLaunch = await walletConnectorService.onDisplayUri(resp.uri);
//         if (!canLaunch) {
//           emit(WalletErrorState(message: AppConstants.metamaskNotInstalled));
//           return;
//         }

//         SessionData? sessionData = await walletConnectorService.authorize(
//           resp,
//           event.signatureFromBackend,
//         );

//         if (sessionData == null) {
//           emit(
//             WalletErrorState(message: AppConstants.userDeniedConnectionRequest),
//           );
//           return;
//         }

//         String walletAddress = NamespaceUtils.getAccount(
//           sessionData.namespaces.values.first.accounts.first,
//         );

//         final String? signatureFromWallet = await walletConnectorService
//             .sendMessageForSigned(
//               resp,
//               walletAddress,
//               sessionData.topic,
//               event.signatureFromBackend,
//             );

//         if (signatureFromWallet == null || signatureFromWallet.isEmpty) {
//           emit(
//             WalletErrorState(message: AppConstants.userDeniedMessageSignature),
//           );
//           return;
//         }

//         emit(
//           WalletReceivedSignatureState(
//             signatureFromWallet: signatureFromWallet,
//             signatureFromBk: event.signatureFromBackend,
//             walletAddress: walletAddress,
//             message: AppConstants.connectionSuccessful,
//           ),
//         );

//         // await walletConnectorService.disconnectWallet(topic: sessionData.topic);
//       } catch (e) {
//         emit(WalletErrorState(message: AppConstants.walletConnectError));
//       }
//     });
//   }
  
// }
// class AppConstants {
//   static const String initializing = "Initializing";
//   static const String initialized = "Initialized";
//   static const String metamaskNotInstalled = "MetaMask not initialized";
//   static const String connectionSuccessful = "Connection Successful";
//   static const String authenticatingPleaseWait = "Authenticating Please Wait!";
//   static const String userDeniedMessageSignature =
//       "User denied message signature";
//   static const String userDeniedConnectionRequest =
//       "User denied connection request";
//   static const String walletConnectError =
//       "Error to connecting the wallet, please try again letter";
//   static const String metamaskLogin = "MetaMask Login";
//   static const String authenticationSuccessful = "Authentication Successful!";
// }
