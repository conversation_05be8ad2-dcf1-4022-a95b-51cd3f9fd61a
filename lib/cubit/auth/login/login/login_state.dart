part of 'login_cubit.dart';

enum LoginStatus { initial, loading, success, failure, newUser }

extension LoginStatusX on LoginStatus {
  bool get isInitial => this == LoginStatus.initial;
  bool get isLoading => this == LoginStatus.loading;
  bool get isSuccess => this == LoginStatus.success;
  bool get isFailure => this == LoginStatus.failure;
  bool get isNewUser => this == LoginStatus.newUser;
}

final class LoginState extends Equatable {
  final LoginStatus status;
  final String? message;
  final RegisterType? registerType;

  const LoginState({this.status = LoginStatus.initial, this.message, this.registerType });

  @override
  List<Object?> get props => [status, message, registerType];

  LoginState copyWith({LoginStatus? status, String? message, RegisterType? registerType}) {
    return LoginState(
      status: status ?? this.status,
      message: message ?? this.message,
      registerType: registerType ?? this.registerType,
    );
  }
}
