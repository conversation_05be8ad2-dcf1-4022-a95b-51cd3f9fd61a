part of 'login_validate_cubit.dart';

final class LoginValidateState extends Equatable {
  final String? errorEmail;
  final String? errorPassword;
  final bool isShowPassword;

  const LoginValidateState({this.errorEmail, this.errorPassword, this.isShowPassword = false});

  @override
  List<Object?> get props => [errorEmail, errorPassword, isShowPassword];

  LoginValidateState copyWith({
    String? errorEmail,
    String? errorPassword,
    bool? isShowPassword,
  }) {
    return LoginValidateState(
      errorEmail: errorEmail ?? this.errorEmail,
      errorPassword: errorPassword ?? this.errorPassword,
      isShowPassword: isShowPassword ?? this.isShowPassword,
    );
  }
}
