part of 'profile_cubit.dart';

enum ProfileStatus { initial, loading, success, failure }

extension ProfileX on ProfileStatus {
  bool get isInitial => this == ProfileStatus.initial;
  bool get isLoading => this == ProfileStatus.loading;
  bool get isSuccess => this == ProfileStatus.success;
  bool get isFailure => this == ProfileStatus.failure;
}

final class ProfileState {
  final UserModel? userModel;
  final ProfileStatus status;
  final String? errorMessage;

  const ProfileState({
    this.userModel,
    this.status = ProfileStatus.initial,
    this.errorMessage,
  });

  ProfileState copyWith({
    UserModel? userModel,
    ProfileStatus? status,
    String? errorMessage,
  }) {
    return ProfileState(
      userModel: userModel ?? this.userModel,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}