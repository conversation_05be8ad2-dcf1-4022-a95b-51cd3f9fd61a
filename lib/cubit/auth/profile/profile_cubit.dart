import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/auth_repository.dart';
import 'package:toii_social/model/user/user_model.dart';

part 'profile_state.dart';

class ProfileCubit extends Cubit<ProfileState> {
  ProfileCubit() : super(const ProfileState());

  final AuthRepository _authRepository = GetIt.instance<AuthRepository>();
  void getProfile() async {
    try {
      final result = await _authRepository.getProfile();
      emit(
        state.copyWith(status: ProfileStatus.success, userModel: result.data),
      );
    } on DioException catch (e) {
      emit(
        state.copyWith(
          status: ProfileStatus.failure,
          errorMessage: e.toString(),
        ),
      );
    } on Exception catch (_) {
      emit(
        state.copyWith(
          status: ProfileStatus.failure,
          errorMessage: "Something went wrong",
        ),
      );
    }
  }
}
