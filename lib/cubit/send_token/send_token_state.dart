part of 'send_token_cubit.dart';

@immutable
class SendTokenState extends Equatable {
  final String? errors;

  final WalletTransferStatus status;

  final String? transactionId;

  final bool loading;

  const SendTokenState(
      {this.errors,
      this.status = WalletTransferStatus.started,
      this.transactionId,
      this.loading = false});

  @override
  List<Object> get props => [loading, status];

  SendTokenState copyWith({
    String? errors,
    WalletTransferStatus? status,
    String? transactionId,
    bool? loading,
  }) {
    return SendTokenState(
      errors: errors ?? this.errors,
      status: status ?? this.status,
      transactionId: transactionId ?? this.transactionId,
      loading: loading ?? this.loading,
    );
  }
}
