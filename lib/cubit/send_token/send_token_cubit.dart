import 'dart:async';
import 'dart:developer' as developer;
import 'dart:math';

import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/chain_repository.dart';
import 'package:toii_social/model/chain/token_item_model.dart';
import 'package:toii_social/utils/navigation_service.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';
import 'package:web3dart/json_rpc.dart';
import 'package:web3dart/web3dart.dart';

part 'send_token_state.dart';

enum WalletTransferStatus { started, confirmed, none }

GetIt _sl = GetIt.instance;

class SendTokenCubit extends Cubit<SendTokenState> {
  SendTokenCubit() : super(const SendTokenState());
  final ChainRepository _chainRepository = _sl<ChainRepository>();

  Future<String?> transfer({
    required TokenItemModel token,
    String? contractAddressHex,
    required String privateKey,
    required String destinationAddress,
    required double quantity,
  }) async {
    final completer = Completer<String?>();
    try {
      emit(state.copyWith(loading: true));
   
      final transactionId = await _chainRepository.sendTransaction(
        privateKey: privateKey,
        type:
            contractAddressHex == null
                ? WalletTransferType.ether
                : WalletTransferType.token,
        amount: quantity.toEthereAmount()!,
        contractAddressHex: null,
        chainId: token.chainId,
        receiver: destinationAddress,
        onTransfer: (from, to, value, transId) {
          developer.log("WalletTransferStatus confirmed");

          // emit(
          //   state.copyWith(
          //     loading: false,
          //     status: WalletTransferStatus.confirmed,
          //     transactionId: transId,
          //   ),
          // );
          
          completer.complete(transId);
        },
        onError: (ex) {
          if (ex is RPCError) {
            emit(
              state.copyWith(
                status: WalletTransferStatus.none,
                errors: ex.toString(),
                loading: false,
              ),
            );
            GetIt.instance<NavigationService>().navigatorContext.showSnackbar(
              message: ex.message,
            );
            developer.log("WalletTransferStatus errors");
            completer.complete(null);
          } else {
            emit(
              state.copyWith(
                status: WalletTransferStatus.none,
                errors: ex.toString(),
                loading: false,
              ),
            );
            GetIt.instance<NavigationService>().navigatorContext.showSnackbar(
              message: ex.toString(),
            );
            developer.log("WalletTransferStatus errors");
            completer.complete(null);
          }
        },
      );
      developer.log("WalletTransferStatus $transactionId");

      emit(state.copyWith(transactionId: transactionId));
    } catch (ex) {
      developer.log("WalletTransferStatus $ex");
      completer.complete(null);
    }
    return completer.future;
  }
}

extension EtheraAmountExtension on double {
  EtherAmount? toEthereAmount() {
    BigInt bigIntValue = BigInt.from(this * pow(10, 18));
    developer.log("BigIntValue: $bigIntValue");
    EtherAmount ethAmount = EtherAmount.fromBigInt(EtherUnit.wei, bigIntValue);
    return ethAmount;
  }
}
