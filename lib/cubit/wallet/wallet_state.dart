part of 'wallet_cubit.dart';

class WalletState extends Equatable {
  final List<TokenItemModel> tokens;
  final double totalBalance;
  const WalletState({ this.tokens = const [], this.totalBalance = 0.0});
  @override
  List<Object?> get props => [ tokens, totalBalance];

  WalletState copyWith({
    List<TokenItemModel>? tokens,
    double? totalBalance,
  }) {
    return WalletState(
      tokens: tokens ?? this.tokens,
      totalBalance: totalBalance ?? this.totalBalance,
    );
  }
}
