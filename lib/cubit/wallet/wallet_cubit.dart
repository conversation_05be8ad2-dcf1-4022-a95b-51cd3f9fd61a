// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:toii_social/core/service/wallet_service.dart';
// import 'package:toii_social/model/chain/chain_model.dart';

// // Wallet State
// abstract class WalletState {}

// class WalletInitial extends WalletState {}

// class WalletLoading extends WalletState {}

// class WalletLoaded extends WalletState {
//   final String walletAddress;
//   final double ethBalance;
//   final double usdBalance;
//   final List<WalletTransaction> transactions;

//   WalletLoaded({
//     required this.walletAddress,
//     required this.ethBalance,
//     required this.usdBalance,
//     required this.transactions,
//   });
// }

// class WalletError extends WalletState {
//   final String message;

//   WalletError(this.message);
// }

// // Wallet Events
// abstract class WalletEvent {}

// class LoadWallet extends WalletEvent {}

// class CreateWallet extends WalletEvent {}

// class ImportWallet extends WalletEvent {
//   final String mnemonic;

//   ImportWallet(this.mnemonic);
// }

// class RefreshBalance extends WalletEvent {}

// class SendTransaction extends WalletEvent {
//   final String toAddress;
//   final double amount;

//   SendTransaction({required this.toAddress, required this.amount});
// }

// // Transaction Model
// class WalletTransaction {
//   final String id;
//   final TransactionType type;
//   final double amount;
//   final String currency;
//   final String address;
//   final DateTime timestamp;
//   final String? txHash;

//   WalletTransaction({
//     required this.id,
//     required this.type,
//     required this.amount,
//     required this.currency,
//     required this.address,
//     required this.timestamp,
//     this.txHash,
//   });
// }

// enum TransactionType { sent, received, pending }

// // Wallet Cubit
// class WalletCubit extends Cubit<WalletState> {
//   WalletCubit() : super(WalletInitial());
//   late ChainModel currentActiveChain;

//   Future<void> loadWallet() async {
//     try {
//       emit(WalletLoading());

//       // Mock data - In real implementation, you would:
//       // 1. Check if wallet exists in secure storage
//       // 2. Load wallet credentials
//       // 3. Fetch balance from blockchain
//       // 4. Load transaction history

//       await Future.delayed(const Duration(seconds: 1)); // Simulate API call

//       final mockTransactions = [
//         WalletTransaction(
//           id: '1',
//           type: TransactionType.received,
//           amount: 0.5,
//           currency: 'ETH',
//           address: '0x1234...5678',
//           timestamp: DateTime.now().subtract(const Duration(hours: 2)),
//           txHash: '0xabc123...',
//         ),
//         WalletTransaction(
//           id: '2',
//           type: TransactionType.sent,
//           amount: 0.25,
//           currency: 'ETH',
//           address: '0x9876...4321',
//           timestamp: DateTime.now().subtract(const Duration(days: 1)),
//           txHash: '0xdef456...',
//         ),
//       ];

//       emit(
//         WalletLoaded(
//           walletAddress: "******************************************",
//           ethBalance: 2.45,
//           usdBalance: 4284.75,
//           transactions: mockTransactions,
//         ),
//       );
//     } catch (e) {
//       emit(WalletError('Failed to load wallet: ${e.toString()}'));
//     }
//   }

//   Future<void> createWallet() async {
//     try {
//       emit(WalletLoading());

//       // Create new wallet using WalletService
//       final credentials = await WalletService.createWallet();

//       // In real implementation:
//       // 1. Save credentials to secure storage
//       // 2. Initialize with zero balance
//       // 3. Create initial transaction history

//       emit(
//         WalletLoaded(
//           walletAddress: credentials.address,
//           ethBalance: 0.0,
//           usdBalance: 0.0,
//           transactions: [],
//         ),
//       );
//     } catch (e) {
//       emit(WalletError('Failed to create wallet: ${e.toString()}'));
//     }
//   }

//   Future<void> importWallet(String mnemonic) async {
//     try {
//       emit(WalletLoading());

//       // Import wallet using mnemonic
//       final credentials = await WalletService.addWallet(mnemonic);

//       // In real implementation:
//       // 1. Validate mnemonic
//       // 2. Derive wallet from mnemonic
//       // 3. Save to secure storage
//       // 4. Fetch balance and transactions

//       emit(
//         WalletLoaded(
//           walletAddress: credentials.address,
//           ethBalance: 1.23,
//           usdBalance: 2154.67,
//           transactions: [],
//         ),
//       );
//     } catch (e) {
//       emit(WalletError('Failed to import wallet: ${e.toString()}'));
//     }
//   }

//   Future<void> refreshBalance() async {
//     final currentState = state;
//     if (currentState is WalletLoaded) {
//       try {
//         // Simulate balance refresh
//         await Future.delayed(const Duration(seconds: 1));

//         // In real implementation:
//         // 1. Query blockchain for current balance
//         // 2. Convert to USD using current exchange rates
//         // 3. Update transaction history

//         emit(
//           WalletLoaded(
//             walletAddress: currentState.walletAddress,
//             ethBalance: currentState.ethBalance + 0.01, // Mock small change
//             usdBalance:
//                 (currentState.ethBalance + 0.01) * 1750, // Mock exchange rate
//             transactions: currentState.transactions,
//           ),
//         );
//       } catch (e) {
//         emit(WalletError('Failed to refresh balance: ${e.toString()}'));
//       }
//     }
//   }

//   Future<void> sendTransaction(String toAddress, double amount) async {
//     final currentState = state;
//     if (currentState is WalletLoaded) {
//       try {
//         emit(WalletLoading());

//         // In real implementation:
//         // 1. Validate address and amount
//         // 2. Check sufficient balance
//         // 3. Create and sign transaction
//         // 4. Broadcast to network
//         // 5. Update local state

//         await Future.delayed(
//           const Duration(seconds: 2),
//         ); // Simulate transaction

//         final newTransaction = WalletTransaction(
//           id: DateTime.now().millisecondsSinceEpoch.toString(),
//           type: TransactionType.pending,
//           amount: amount,
//           currency: 'ETH',
//           address: toAddress,
//           timestamp: DateTime.now(),
//           txHash:
//               '0x${DateTime.now().millisecondsSinceEpoch.toRadixString(16)}',
//         );

//         final updatedTransactions = [
//           newTransaction,
//           ...currentState.transactions,
//         ];

//         emit(
//           WalletLoaded(
//             walletAddress: currentState.walletAddress,
//             ethBalance: currentState.ethBalance - amount,
//             usdBalance: (currentState.ethBalance - amount) * 1750,
//             transactions: updatedTransactions,
//           ),
//         );
//       } catch (e) {
//         emit(WalletError('Failed to send transaction: ${e.toString()}'));
//       }
//     }
//   }
// }
import 'dart:convert';
import 'dart:developer';

import 'package:collection/collection.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/chain_api_repository.dart';
import 'package:toii_social/core/repository/chain_repository.dart';
import 'package:toii_social/core/repository/coin_market_repository.dart';
import 'package:toii_social/model/chain/chain_model.dart';
import 'package:toii_social/model/chain/token_item_model.dart';
import 'package:toii_social/utils/shared_prefs/shared_prefs.dart';

part 'wallet_state.dart';

const keySelectedChain = 'selected_chain';
final tokenChainKey = "KEY_TOKENCHAIN";
final listTokenKey = "KEY_LISTTOKEN";

class WalletCubit extends Cubit<WalletState> {
  final ChainRepository _chainRepository = GetIt.instance<ChainRepository>();
  final ChainApiRepository _chainApiRepository =
      GetIt.instance<ChainApiRepository>();
  final CoinMarketRepository _coinMarketRepository =
      GetIt.instance<CoinMarketRepository>();

  String _walletAddress = "";
  List<ChainModel> listChain = [];
  WalletCubit() : super(WalletState()) {
    // initData();
  }

  void initData() async {
    listChain = await _chainApiRepository.getAllChain();
  }

  void getToken({required String walletAddress}) async {
    _walletAddress = walletAddress;
    // final currentActiveChain = state.currentActiveChain;
    final tokens = await _getTokenLocalByChain();
    var sum = tokens.fold(
      0.0,
      (sum, next) => sum + ((next.balance ?? 0) * (next.price ?? 0.0)),
    );

    emit(state.copyWith(tokens: tokens, totalBalance: sum));
  }

  Future<List<TokenItemModel>> _getTokenLocalByChain() async {
    try {
      listChain = await _chainApiRepository.getAllChain();
      final result = await _coinMarketRepository.getListingLatest();
      SharedPref().clearKey(listTokenKey);
      final listTokenStr = SharedPref.getString(listTokenKey);
      List<dynamic> tokenJson =
          listTokenStr.isEmpty ? [] : json.decode(listTokenStr);
      List<TokenItemModel> lists = [];
      if (tokenJson.isEmpty) {
        for (var chain in listChain) {
          final item = TokenItemModel(
            chainId: chain.chainId,
            address: "",
            symbol: chain.nativeSymbol,
            name: chain.name,
            isHidden: false,
            isNative: true,
            chainName:
                chain.shortName.isNotEmpty ? chain.shortName : chain.name,
          );
          final nativeBalance = await _chainRepository.fetchBalanceFromChain(
            _walletAddress,
            chain,
          );
          item.balance = nativeBalance;

          final itemCurrency = result.token.firstWhereOrNull(
            (element) =>
                element.symbol?.toLowerCase() == item.symbol?.toLowerCase(),
          );
          if (itemCurrency != null) {
            item.price = itemCurrency.quoteModel?.usd?.price;
            item.idLogo = itemCurrency.id!;
          }

          lists.add(item);
        }
        String listTokens = jsonEncode(lists);
        SharedPref.setString(listTokenKey, listTokens);
      } else {
        for (var token in tokenJson) {
          final item = TokenItemModel.fromJson(token);
          final chain =
              listChain
                  .where((chain) => chain.chainId == item.chainId)
                  .firstOrNull;
          if (chain != null) {
            final nativeBalance = await _chainRepository.fetchBalanceFromChain(
              _walletAddress,
              chain,
            );
            item.balance = nativeBalance;
          }

          lists.add(item);
        }
      }
      return lists;
    } catch (e) {
      SharedPref().clearKey(listTokenKey);
      // _getTokenLocalByChain();
      log(e.toString());
      return [];
    }

    //   final item = TokenItemModel(
    //     chainId: chain.chainId,
    //     address: element.value['address'],
    //     symbol: element.value['symbol'],
    //     name: element.value['name'],
    //     isHidden: element.value['isHidden'] ?? false,
    //     isNative: element.value['isNative'] ?? false,
    //   );
    //   final balance = await _chainRepository.fetchBalanceOfToken(
    //     _walletAddress,
    //     item.address ?? "",
    //     state.currentActiveChain,
    //   );
    //   item.balance = balance;
    //   lists.add(item);
    // }
  }

  // for (var item in lists) {
  //   //   if (item.symbol == state.currentActiveChain.symbol) {
  //   final nativeBalance = await _chainRepository.fetchBalanceFromChain(
  //     _walletAddress,
  //     state.currentActiveChain,
  //   );
  //   item.balance = nativeBalance;
  //   // } else {
  //   //   final balance = await _chainRepository.fetchBalanceOfToken(
  //   //     _walletAddress,
  //   //     item.address ?? "",
  //   //     state.currentActiveChain,
  //   //   );
  //   //   item.balance = balance;
  //   // }
  // }
}
