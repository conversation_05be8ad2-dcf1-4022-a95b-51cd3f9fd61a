import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/social_repository.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/model/post/repost_request.dart';
import 'package:toii_social/screen/home/<USER>/enum/privacy_option_enum.dart';

part 'repost_state.dart';

class RepostCubit extends Cubit<RepostState> {
  RepostCubit() : super(const RepostState());

  final SocialRepository _socialRepository = GetIt.instance<SocialRepository>();

  /// Repost một bài viết nguyên văn
  Future<void> repost({
    required String originalPostId,
    PrivacyOption privacy = PrivacyOption.everyone,
    List<String>? allowedUserIds,
  }) async {
    emit(state.copyWith(status: RepostStatus.loading));
    EasyLoading.show();
    try {
      final request = RepostRequest(
        originalPostId: originalPostId,
        privacy: privacy.apiValue,
        allowedUserIds: allowedUserIds,
      );

      await _socialRepository.repostPost(request);

      emit(state.copyWith(status: RepostStatus.success));
    } catch (e) {
      emit(
        state.copyWith(
          status: RepostStatus.failure,
          errorMessage: e.toString(),
        ),
      );
    } finally {
      EasyLoading.dismiss();
    }
  }

  /// Quote repost - chia sẻ bài viết kèm bình luận
  /// Tính năng này sẽ được implement sau
  Future<void> quoteRepost({
    required String originalPostId,
    required String content,
    List<String>? mediaKeys,
    PrivacyOption privacy = PrivacyOption.everyone,
    List<String>? allowedUserIds,
  }) async {
    emit(state.copyWith(status: RepostStatus.loading));
    EasyLoading.show();
    try {
      final request = RepostRequest(
        originalPostId: originalPostId,
        repostContent: content,
        mediaKeys: mediaKeys,
        privacy: privacy.apiValue,
        allowedUserIds: allowedUserIds,
      );

      await _socialRepository.repostPost(request);

      emit(state.copyWith(status: RepostStatus.success));
    } catch (e) {
      emit(
        state.copyWith(
          status: RepostStatus.failure,
          errorMessage: e.toString(),
        ),
      );
    } finally {
      EasyLoading.dismiss();
    }
  }

  /// Reset state về initial
  void resetState() {
    emit(const RepostState());
  }
}
