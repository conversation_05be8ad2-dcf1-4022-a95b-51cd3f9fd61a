part of 'repost_cubit.dart';

enum RepostStatus { initial, loading, success, failure }

extension RepostStatusX on RepostStatus {
  bool get isInitial => this == RepostStatus.initial;
  bool get isLoading => this == RepostStatus.loading;
  bool get isSuccess => this == RepostStatus.success;
  bool get isFailure => this == RepostStatus.failure;
}

class RepostState extends Equatable {
  const RepostState({
    this.status = RepostStatus.initial,
    this.repostedPost,
    this.errorMessage,
  });

  final RepostStatus status;
  final PostModel? repostedPost;
  final String? errorMessage;

  RepostState copyWith({
    RepostStatus? status,
    PostModel? repostedPost,
    String? errorMessage,
  }) {
    return RepostState(
      status: status ?? this.status,
      repostedPost: repostedPost ?? this.repostedPost,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [status, repostedPost, errorMessage];
}
