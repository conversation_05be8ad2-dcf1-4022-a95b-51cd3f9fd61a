import 'dart:io';

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:toii_social/core/repository/social_repository.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/model/post/post_request.dart';
import 'package:toii_social/screen/home/<USER>/enum/privacy_option_enum.dart';

part 'create_post_state.dart';

class CreatePostCubit extends Cubit<CreatePostState> {
  CreatePostCubit() : super(const CreatePostState());
  final SocialRepository _socialRepository = GetIt.instance<SocialRepository>();
  final ImagePicker _imagePicker = ImagePicker();
  final ImageCropper _imageCropper = ImageCropper();

  Future<void> pickImages() async {
    try {
      final List<XFile> images = await _imagePicker.pickMultiImage(
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );
      if (images.isNotEmpty) {
        final List<String> croppedImagePaths = [];

        for (final image in images) {
          final croppedFile = await _cropImage(image.path);
          if (croppedFile != null) {
            croppedImagePaths.add(croppedFile.path);
          }
        }

        if (croppedImagePaths.isNotEmpty) {
          final currentPaths = List<String>.from(state.selectedImagePaths);
          currentPaths.addAll(croppedImagePaths);
          emit(
            state.copyWith(
              selectedImagePaths: currentPaths,
              imageUploadStatus: ImageUploadStatus.initial,
              imageUploadError: null,
            ),
          );
        }
      }
    } catch (e) {
      print('Error picking images: $e');
      emit(
        state.copyWith(
          imageUploadStatus: ImageUploadStatus.failure,
          imageUploadError: 'Failed to pick images: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> pickImageFromCamera() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );
      if (image != null) {
        final croppedFile = await _cropImage(image.path);
        if (croppedFile != null) {
          final currentPaths = List<String>.from(state.selectedImagePaths);
          currentPaths.add(croppedFile.path);
          emit(
            state.copyWith(
              selectedImagePaths: currentPaths,
              imageUploadStatus: ImageUploadStatus.initial,
              imageUploadError: null,
            ),
          );
        }
      }
    } catch (e) {
      print('Error taking photo: $e');
      emit(
        state.copyWith(
          imageUploadStatus: ImageUploadStatus.failure,
          imageUploadError: 'Failed to take photo: ${e.toString()}',
        ),
      );
    }
  }

  Future<CroppedFile?> _cropImage(String imagePath) async {
    try {
      final croppedFile = await _imageCropper.cropImage(
        sourcePath: imagePath,
        uiSettings: [
          AndroidUiSettings(
            // toolbarTitle: 'Crop Image',
            toolbarColor: const Color(0xFF1A1A1A),
            toolbarWidgetColor: const Color(0xFFFFFFFF),
            backgroundColor: const Color(0xFF000000),
            activeControlsWidgetColor: const Color(0xFF007AFF),
            cropFrameColor: const Color(0xFF007AFF),
            cropGridColor: const Color(0x80FFFFFF),
            initAspectRatio: CropAspectRatioPreset.ratio4x3,
            lockAspectRatio: true, // Khóa aspect ratio để đảm bảo nhất quán
            hideBottomControls: false,
            showCropGrid: true,
            aspectRatioPresets: [
              CropAspectRatioPreset.square,
              CropAspectRatioPreset.ratio4x3,
              CropAspectRatioPreset.ratio16x9,
              CropAspectRatioPreset.original,
            ],
          ),
          IOSUiSettings(
            // title: 'Crop Image',
            doneButtonTitle: 'Done',
            cancelButtonTitle: 'Cancel',
            aspectRatioPickerButtonHidden: false,
            resetButtonHidden: false,
            rotateButtonsHidden: false,
            aspectRatioLockEnabled: true, // Khóa aspect ratio cho iOS
            resetAspectRatioEnabled: true,
            rotateClockwiseButtonHidden: false,
            hidesNavigationBar: false,
            aspectRatioPresets: [
              CropAspectRatioPreset.square,
              CropAspectRatioPreset.ratio4x3,
              CropAspectRatioPreset.ratio16x9,
              CropAspectRatioPreset.original,
            ],
          ),
        ],
        compressFormat: ImageCompressFormat.jpg,
        compressQuality: 85,
        maxWidth: 1920,
        maxHeight: 1080,
      );
      return croppedFile;
    } catch (e) {
      print('Error cropping image: $e');
      return null;
    }
  }

  void removeImage(int index, bool isLocal) {
    if (isLocal) {
      final currentPaths = List<String>.from(state.selectedImagePaths);
      if (index >= 0 && index < currentPaths.length) {
        currentPaths.removeAt(index);
        emit(state.copyWith(selectedImagePaths: currentPaths));
      }
    } else {
      final currentUrls = List<String>.from(state.existingMediaUrls);
      if (index >= 0 && index < currentUrls.length) {
        currentUrls.removeAt(index);
        // When removing a remote image, we also need to remove its corresponding key
        final updatedKeys = List<String>.from(state.uploadedMediaKeys);
        if (index < updatedKeys.length) {
          updatedKeys.removeAt(index);
        }
        emit(
          state.copyWith(
            existingMediaUrls: currentUrls,
            uploadedMediaKeys: updatedKeys,
          ),
        );
      }
    }
  }

  Future<void> uploadImages() async {
    if (state.selectedImagePaths.isEmpty) return;

    emit(state.copyWith(imageUploadStatus: ImageUploadStatus.loading));

    try {
      final List<String> mediaKeys = [];

      for (final imagePath in state.selectedImagePaths) {
        final file = File(imagePath);
        final response = await _socialRepository.uploadMedia(file, 'image');
        mediaKeys.add(response.data.key ?? '');
      }

      // Combine newly uploaded keys with existing keys
      final updatedKeys = List<String>.from(state.uploadedMediaKeys);
      updatedKeys.addAll(mediaKeys);

      emit(
        state.copyWith(
          imageUploadStatus: ImageUploadStatus.success,
          uploadedMediaKeys: updatedKeys,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          imageUploadStatus: ImageUploadStatus.failure,
          imageUploadError: 'Failed to upload images',
        ),
      );
    }
  }

  void setPrivacy(PrivacyOption privacy) {
    emit(state.copyWith(privacy: privacy));
  }

  Future<void> createAPost({String? content}) async {
    emit(state.copyWith(status: CreatePostStatus.loading));
    try {
      // Upload images first if any are selected
      if (state.selectedImagePaths.isNotEmpty &&
          state.uploadedMediaKeys.isEmpty) {
        await uploadImages();
        if (state.imageUploadStatus == ImageUploadStatus.failure) {
          emit(
            state.copyWith(
              status: CreatePostStatus.failure,
              errorMessage: state.imageUploadError ?? 'Failed to upload images',
            ),
          );
          return;
        }
      }

      final request = CreatePostRequestModel(
        content: content,
        privacy: state.privacy.apiValue,
        mediaKeys: state.uploadedMediaKeys,
      );
      final post = await _socialRepository.createNewPost(request);

      emit(state.copyWith(status: CreatePostStatus.success, post: post.data));
    } catch (e) {
      emit(
        state.copyWith(
          status: CreatePostStatus.failure,
          errorMessage: 'Failed to create post',
        ),
      );
    }
  }

  void initUpdatePost(PostModel? post) {
    if (post == null) return;

    // Initialize the state with the post's existing data
    emit(
      state.copyWith(
        privacy: PrivacyOptionExt.fromApiValue(post.privacy),
        uploadedMediaKeys: post.mediaKeys,
        existingMediaUrls: post.mediaUrls,
      ),
    );
  }

  Future<void> updatePost({
    required String id,
    String? content,
    String? privacy,
  }) async {
    emit(state.copyWith(status: CreatePostStatus.loading));
    try {
      // Upload images first if any are selected
      if (state.selectedImagePaths.isNotEmpty) {
        await uploadImages();
        if (state.imageUploadStatus == ImageUploadStatus.failure) {
          emit(
            state.copyWith(
              status: CreatePostStatus.failure,
              errorMessage: state.imageUploadError ?? 'Failed to upload images',
            ),
          );
          return;
        }
      }

      // Use the current state's uploadedMediaKeys which should contain both
      // existing and newly uploaded media keys
      final postModel = PostRequest(
        content: content,
        mediaKeys: state.uploadedMediaKeys,
        privacy: privacy ?? state.privacy.apiValue,
      );

      await _socialRepository.updatePost(id, postModel);
      emit(state.copyWith(status: CreatePostStatus.success));
    } catch (e) {
      emit(
        state.copyWith(
          status: CreatePostStatus.failure,
          errorMessage: 'Failed to update post: ${e.toString()}',
        ),
      );
    }
  }
}
