part of 'comment_cubit.dart';

enum CommentStatus { initial, loading, success, failure }

extension CommentStatusX on CommentStatus {
  bool get isInitial => this == CommentStatus.initial;
  bool get isLoading => this == CommentStatus.loading;
  bool get isSuccess => this == CommentStatus.success;
  bool get isFailure => this == CommentStatus.failure;
}

class CommentState extends Equatable {
  final CommentStatus status;
  final List<CommentItemModel> comments;
  final String? errorMessage;

  const CommentState({
    this.status = CommentStatus.initial,
    this.comments = const [],
    this.errorMessage,
  });

  CommentState copyWith({
    CommentStatus? status,
    List<CommentItemModel>? comments,
    String? errorMessage,
  }) {
    return CommentState(
      status: status ?? this.status,
      comments: comments ?? this.comments,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [status, comments, errorMessage];
}
