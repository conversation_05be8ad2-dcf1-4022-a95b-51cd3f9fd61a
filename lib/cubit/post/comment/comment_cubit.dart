import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/social_repository.dart';
import 'package:toii_social/model/comment/comment_model.dart';

part 'comment_state.dart';

class CommentCubit extends Cubit<CommentState> {
  final String postId;
  CommentCubit({required this.postId}) : super(const CommentState());
  final SocialRepository _socialRepository = GetIt.instance<SocialRepository>();

  Future<void> getComments() async {
    emit(state.copyWith(status: CommentStatus.loading));
    try {
      final comments = await _socialRepository.getCommentsOfPost(postId);

      emit(
        state.copyWith(
          status: CommentStatus.success,
          comments: comments.data.comments,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: CommentStatus.failure,
          errorMessage: 'Failed to load comments',
        ),
      );
    }
  }

  void addComment(CommentItemModel comment) {
    final List<CommentItemModel> updatedComments = List.from(state.comments)
      ..insert(0, comment);
    emit(state.copyWith(comments: updatedComments));
  }
}
