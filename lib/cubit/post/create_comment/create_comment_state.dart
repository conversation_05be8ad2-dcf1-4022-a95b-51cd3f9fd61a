part of 'create_comment_cubit.dart';

enum CreateCommentStatus { initial, loading, success, failure }

extension CreateCommentStatusX on CreateCommentStatus {
  bool get isInitial => this == CreateCommentStatus.initial;
  bool get isLoading => this == CreateCommentStatus.loading;
  bool get isSuccess => this == CreateCommentStatus.success;
  bool get isFailure => this == CreateCommentStatus.failure;
}

class CreateCommentState extends Equatable {
  final CreateCommentStatus status;
  final CommentItemModel? comment;
  final String? errorMessage;

  const CreateCommentState({
    this.status = CreateCommentStatus.initial,
    this.comment,
    this.errorMessage,
  });

  CreateCommentState copyWith({
    CreateCommentStatus? status,
    CommentItemModel? comment,
    String? errorMessage,
  }) {
    return CreateCommentState(
      status: status ?? this.status,
      comment: comment ?? this.comment,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [status, comment, errorMessage];
}
