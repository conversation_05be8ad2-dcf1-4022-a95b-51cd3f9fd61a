import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/social_repository.dart';
import 'package:toii_social/model/comment/comment_model.dart';

part 'create_comment_state.dart';

class CreateCommentCubit extends Cubit<CreateCommentState> {
  final String postId;
  CreateCommentCubit({required this.postId})
    : super(const CreateCommentState());
  final SocialRepository _socialRepository = GetIt.instance<SocialRepository>();

  Future<void> createAComment({
    required String content,
   
  }) async {
    emit(state.copyWith(status: CreateCommentStatus.loading));
    try {
      final request = CreateCommentRequestModel(content: content);
      final comment = await _socialRepository.createCommentsOfPost(
        postId,
        request,
      );

      emit(
        state.copyWith(
          status: CreateCommentStatus.success,
          comment: comment.data,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: CreateCommentStatus.failure,
          errorMessage: 'Failed to load comments',
        ),
      );
    }
  }
}
