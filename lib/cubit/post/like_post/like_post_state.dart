part of 'like_post_cubit.dart';

enum LikePostStatus { initial, loading, success, failure }

class LikePostState {
  final LikePostStatus status;
  final bool isLiked;
  final String? errorMessage;
  final int likeCount;

  const LikePostState({
    this.status = LikePostStatus.initial,
    this.isLiked = false,
    this.errorMessage,
    this.likeCount = 0,
  });

  LikePostState copyWith({
    LikePostStatus? status,
    bool? isLiked,
    String? errorMessage,
    int? likeCount,
  }) {
    return LikePostState(
      status: status ?? this.status,
      isLiked: isLiked ?? this.isLiked,
      errorMessage: errorMessage ?? this.errorMessage,
      likeCount: likeCount ?? this.likeCount,
    );
  }
}
