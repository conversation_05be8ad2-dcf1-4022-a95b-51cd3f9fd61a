import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/social_repository.dart';
import 'package:toii_social/model/post/post_model.dart';

part 'like_post_state.dart';

class LikePostCubit extends Cubit<LikePostState> {
  final String postId;
  final PostModel post;
  final SocialRepository _socialRepository = GetIt.instance<SocialRepository>();

  LikePostCubit({
    required this.postId,
    required this.post,
    bool initialLiked = false,
  }) : super(LikePostState(isLiked: initialLiked, likeCount: post.actionlikes));

  Future<void> toggleLike() async {
    final currentlyLiked = state.isLiked;
    final currentLikeCount = state.likeCount;
    final newLikeCount =
        currentlyLiked ? currentLikeCount - 1 : currentLikeCount + 1;

    // Change UI immediately
    emit(
      state.copyWith(
        status: LikePostStatus.success,
        isLiked: !currentlyLiked,
        likeCount: newLikeCount,
        errorMessage: null,
      ),
    );

    // Send API request asynchronously without waiting for response
    try {
      if (!currentlyLiked) {
        _socialRepository.likePost(postId); // Remove await
      } else {
        _socialRepository.unlikePost(postId); // Remove await
      }
    } catch (e) {
      // Silently handle error - UI already changed
      // Optionally log error for debugging
      print('Like/Unlike API error: $e');
    }
  }
}
