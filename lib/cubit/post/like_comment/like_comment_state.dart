part of 'like_comment_cubit.dart';

enum LikeCommentStatus { initial, loading, success, failure }

class LikeCommentState {
  final LikeCommentStatus status;
  final bool isLiked;
  final String? errorMessage;
  final int likeCount;

  const LikeCommentState({
    this.status = LikeCommentStatus.initial,
    this.isLiked = false,
    this.errorMessage,
    this.likeCount = 0,
  });

  LikeCommentState copyWith({
    LikeCommentStatus? status,
    bool? isLiked,
    String? errorMessage,
    int? likeCount,
  }) {
    return LikeCommentState(
      status: status ?? this.status,
      isLiked: isLiked ?? this.isLiked,
      errorMessage: errorMessage ?? this.errorMessage,
      likeCount: likeCount ?? this.likeCount,
    );
  }
}
