import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/social_repository.dart';

part 'like_comment_state.dart';

class LikeCommentCubit extends Cubit<LikeCommentState> {
  final String commentId;
  final SocialRepository _socialRepository = GetIt.instance<SocialRepository>();

  LikeCommentCubit({
    required this.commentId,
    bool initialLiked = false,
    int initialLikeCount = 0,
  }) : super(
         LikeCommentState(isLiked: initialLiked, likeCount: initialLikeCount),
       );

  Future<void> toggleLike() async {
    final currentlyLiked = state.isLiked;
    final currentLikeCount = state.likeCount;
    final newLikeCount =
        currentlyLiked ? currentLikeCount - 1 : currentLikeCount + 1;

    
    emit(
      state.copyWith(
        status: LikeCommentStatus.success,
        isLiked: !currentlyLiked,
        likeCount: newLikeCount,
        errorMessage: null,
      ),
    );

    try {
      if (!currentlyLiked) {
        _socialRepository.likeComment(commentId);
      } else {
        _socialRepository.unlikeComment(commentId);
      }
    } catch (e) {
      // Silently handle error - UI already changed
      print('Like/Unlike comment API error: $e');
    }
  }
}
