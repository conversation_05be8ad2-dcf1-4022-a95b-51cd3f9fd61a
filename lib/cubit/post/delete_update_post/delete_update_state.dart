enum DeleteUpdatePostStatus { initial, loading, success, failure }

class DeleteUpdatePostState {
  final DeleteUpdatePostStatus status;
  final String? message;
  final String? errorMessage;

  const DeleteUpdatePostState({
    this.status = DeleteUpdatePostStatus.initial,
    this.message,
    this.errorMessage,
  });

  DeleteUpdatePostState copyWith({
    DeleteUpdatePostStatus? status,
    String? message,
    String? errorMessage,
  }) {
    return DeleteUpdatePostState(
      status: status ?? this.status,
      message: message ?? this.message,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}
