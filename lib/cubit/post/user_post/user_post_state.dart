import 'package:equatable/equatable.dart';
import 'package:toii_social/model/post/post_model.dart';

enum UserPostStatus { initial, loading, loaded, error }

enum UserRepostStatus { initial, loading, loaded, error }

extension UserPostStatusX on UserPostStatus {
  bool get isInitial => this == UserPostStatus.initial;
  bool get isLoading => this == UserPostStatus.loading;
  bool get isLoaded => this == UserPostStatus.loaded;
  bool get isError => this == UserPostStatus.error;
}

extension UserRepostStatusX on UserRepostStatus {
  bool get isInitial => this == UserRepostStatus.initial;
  bool get isLoading => this == UserRepostStatus.loading;
  bool get isLoaded => this == UserRepostStatus.loaded;
  bool get isError => this == UserRepostStatus.error;
}

class UserPostState extends Equatable {
  final UserPostStatus postStatus;
  final UserRepostStatus repostStatus;
  final PostResponseDataModel? posts;
  final PostResponseDataModel? reposts;
  final String? errorMessage;

  const UserPostState({
    this.postStatus = UserPostStatus.initial,
    this.repostStatus = UserRepostStatus.initial,
    this.posts,
    this.reposts,
    this.errorMessage,
  });

  UserPostState copyWith({
    UserPostStatus? postStatus,
    UserRepostStatus? repostStatus,
    PostResponseDataModel? posts,
    PostResponseDataModel? reposts,
    String? errorMessage,
  }) {
    return UserPostState(
      postStatus: postStatus ?? this.postStatus,
      repostStatus: repostStatus ?? this.repostStatus,
      posts: posts ?? this.posts,
      reposts: reposts ?? this.reposts,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [
    postStatus,
    repostStatus,
    posts,
    reposts,
    errorMessage,
  ];
}
