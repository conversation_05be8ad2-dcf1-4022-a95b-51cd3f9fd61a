import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_social/core/repository/social_repository.dart';
import 'package:toii_social/cubit/post/user_post/user_post_state.dart';

class UserPostCubit extends Cubit<UserPostState> {
  final SocialRepository socialRepository;

  UserPostCubit({required this.socialRepository})
    : super(const UserPostState());

  Future<void> getUserPosts(String userId) async {
    try {
      emit(state.copyWith(postStatus: UserPostStatus.loading));
      final response = await socialRepository.getUserPosts(userId);

      emit(
        state.copyWith(
          postStatus: UserPostStatus.loaded,
          posts: response.data,
          errorMessage: null,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          postStatus: UserPostStatus.error,
          errorMessage: e.toString(),
        ),
      );
    }
  }

  Future<void> getUserReposts(String userId) async {
    try {
      emit(state.copyWith(repostStatus: UserRepostStatus.loading));
      final response = await socialRepository.getUserReposts(userId);

      emit(
        state.copyWith(
          repostStatus: UserRepostStatus.loaded,
          reposts: response.data,
          errorMessage: null,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          repostStatus: UserRepostStatus.error,
          errorMessage: e.toString(),
        ),
      );
    }
  }
}
