import 'package:equatable/equatable.dart';

enum HidePostStatus { initial, loading, success, failure }

extension HidePostStatusX on HidePostStatus {
  bool get isInitial => this == HidePostStatus.initial;
  bool get isLoading => this == HidePostStatus.loading;
  bool get isSuccess => this == HidePostStatus.success;
  bool get isFailure => this == HidePostStatus.failure;
}

class HidePostState extends Equatable {
  final HidePostStatus status;
  final String? errorMessage;
  final String? postId;

  const HidePostState({
    this.status = HidePostStatus.initial,
    this.errorMessage,
    this.postId,
  });

  HidePostState copyWith({
    HidePostStatus? status,
    String? errorMessage,
    String? postId,
  }) {
    return HidePostState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      postId: postId ?? this.postId,
    );
  }

  @override
  List<Object?> get props => [status, errorMessage, postId];
}
