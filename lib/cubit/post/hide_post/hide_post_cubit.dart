import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/social_repository.dart';
import 'package:toii_social/cubit/post/hide_post/hide_post_state.dart';

class HidePostCubit extends Cubit<HidePostState> {
  final SocialRepository _socialRepository = GetIt.instance<SocialRepository>();

  HidePostCubit() : super(const HidePostState());

  Future<void> hidePost(String postId) async {
    try {
      emit(state.copyWith(status: HidePostStatus.loading));

      await _socialRepository.hidePost(postId);

      emit(state.copyWith(status: HidePostStatus.success, postId: postId));
    } catch (error) {
      emit(
        state.copyWith(
          status: HidePostStatus.failure,
          errorMessage: error.toString(),
        ),
      );
    }
  }
}
