import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/social_repository.dart';
import 'package:toii_social/model/post/post_model.dart';

part 'home_state.dart';

class HomeCubit extends Cubit<HomeState> {
  HomeCubit() : super(const HomeState());

  final SocialRepository _socialRepository = GetIt.instance<SocialRepository>();
  void getUserFeed() async {
    try {
      emit(state.copyWith(status: HomeStatus.loading));

      final result = await _socialRepository.getUserFeed();

      emit(
        state.copyWith(status: HomeStatus.success, posts: result.data.posts),
      );
    } on DioException catch (e) {
      emit(
        state.copyWith(status: HomeStatus.failure, errorMessage: e.toString()),
      );
    } on Exception catch (_) {
      emit(
        state.copyWith(
          status: HomeStatus.failure,
          errorMessage: "Something went wrong",
        ),
      );
    }
  }

  void addPost(PostModel post) {
    final List<PostModel> updatedPosts = List.from(state.posts)
      ..insert(0, post);
    emit(state.copyWith(posts: updatedPosts));
  }

  void updatePost(PostModel updatedPost) {
    final List<PostModel> updatedPosts =
        state.posts
            .map((post) => post.id == updatedPost.id ? updatedPost : post)
            .toList();
    emit(state.copyWith(posts: updatedPosts));
  }

  void updatePostLikeCount(String postId, int newLikeCount, bool isLiked) {
    final List<PostModel> updatedPosts =
        state.posts.map((post) {
          if (post.id == postId) {
            // Update the reactions to reflect the new like count
            final updatedReactions =
                post.reactions?.reactions?.map((reaction) {
                  if (reaction.type?.toLowerCase() == 'love') {
                    return reaction.copyWith(count: newLikeCount);
                  }
                  return reaction;
                }).toList() ??
                [];

            final updatedUserReactions =
                isLiked
                    ? [...(post.reactions?.userReactions ?? []), 'love']
                    : post.reactions?.userReactions
                            ?.where((r) => r != 'love')
                            .toList() ??
                        [];

            return post.copyWith(
              reactions: post.reactions?.copyWith(
                reactions: updatedReactions,
                userReactions: updatedUserReactions.cast<String>(),
              ),
            );
          }
          return post;
        }).toList();

    emit(state.copyWith(posts: updatedPosts));
  }
}
