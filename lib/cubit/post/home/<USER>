part of 'home_cubit.dart';

enum HomeStatus { initial, loading, success, failure }

extension HomeX on HomeStatus {
  bool get isInitial => this == HomeStatus.initial;
  bool get isLoading => this == HomeStatus.loading;
  bool get isSuccess => this == HomeStatus.success;
  bool get isFailure => this == HomeStatus.failure;
}

final class HomeState {
  final List<PostModel> posts;
  final HomeStatus status;
  final String? errorMessage;

  const HomeState({
    this.posts = const [],
    this.status = HomeStatus.initial,
    this.errorMessage,
  });

  HomeState copyWith({
    List<PostModel>? posts,
    HomeStatus? status,
    String? errorMessage,
  }) {
    return HomeState(
      posts: posts ?? this.posts,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}