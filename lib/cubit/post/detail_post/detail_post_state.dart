part of 'detail_post_cubit.dart';

enum DetailPostStatus { initial, loading, success, failure }

class DetailPostState {
  final DetailPostStatus status;
  final PostModel? post;
  final String? errorMessage;

  const DetailPostState({
    this.status = DetailPostStatus.initial,
    this.post,
    this.errorMessage,
  });

  DetailPostState copyWith({
    DetailPostStatus? status,
    PostModel? post,
    String? errorMessage,
  }) {
    return DetailPostState(
      status: status ?? this.status,
      post: post ?? this.post,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}
