import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/social_repository.dart';
import 'package:toii_social/model/post/post_model.dart';

part 'detail_post_state.dart';

class DetailPostCubit extends Cubit<DetailPostState> {
  final SocialRepository _socialRepository = GetIt.instance<SocialRepository>();
  DetailPostCubit() : super(const DetailPostState());

  Future<void> fetchPostDetail(String postId) async {
    emit(state.copyWith(status: DetailPostStatus.loading));
    try {
      final response = await _socialRepository.getPostById(postId);
      emit(
        state.copyWith(
          status: DetailPostStatus.success,
          post: response.data,
          errorMessage: null,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: DetailPostStatus.failure,
          errorMessage: e.toString(),
        ),
      );
    }
  }
}
